import os
import openai
from dotenv import load_dotenv

load_dotenv(".env")

class DeepSeek:
    def __init__(self, model_name=None):
        self.model_name = model_name or os.getenv('DEEPSEEK_MODEL', 'deepseek-reasoner')
        self.api_key = os.getenv('DEEPSEEK_API_KEY')

        # Configure OpenAI client for DeepSeek API
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        )

    def generate(self, prompt, max_tokens=1500, temperature=0.7):
        """Generate text using DeepSeek model via OpenAI-compatible API"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"DeepSeek API error: {e}")
            return f"Error generating response: {e}"
