from typing import List, Dict, Any
from verbatim_quote_extractor import VerbatimQuoteExtractor, MilitantJournalismSegment

class TranscriptProcessor:
    """Enhanced transcript processor with verbatim quote verification"""

    def __init__(self):
        self.quote_extractor = VerbatimQuoteExtractor()
        self.verification_enabled = True

    def process_transcript(self, transcript_text: str) -> List[Dict[str, Any]]:
        """Process transcript with verbatim quote verification"""
        print("Processing transcript with verbatim quote verification...")

        if not transcript_text:
            return []

        # Step 1: Extract verbatim quotes from the original transcript
        verbatim_quotes = self.quote_extractor.extract_verbatim_quotes(transcript_text)
        print(f"Extracted {len(verbatim_quotes)} verbatim quotes")

        # Step 2: Create segments using verbatim quotes
        militant_segments = self.quote_extractor.create_militant_segments(verbatim_quotes)
        print(f"Created {len(militant_segments)} segments")

        # Step 3: Verify all quotes are authentic
        if self.verification_enabled:
            verification_report = self.quote_extractor.verify_quotes_authenticity(
                militant_segments, transcript_text
            )
            print("Verification Report:")
            print(f"  - Total segments: {verification_report['total_segments']}")
            print(f"  - Verified segments: {verification_report['verified_segments']}")
            print(f"  - Authenticity score: {verification_report['authenticity_score']:.2%}")

            if verification_report['failed_verifications']:
                print("  - Failed verifications:")
                for failure in verification_report['failed_verifications']:
                    print(f"    * Segment {failure['segment_index']}: {failure['segment_title']}")
                    for quote in failure['failed_quotes']:
                        print(f"      - {quote}")

        # Step 4: Convert to app format
        processed_segments = []
        for i, segment in enumerate(militant_segments):
            # estimated_start and estimated_end are already in HH:MM:SS format
            start_ref = segment.estimated_start
            end_ref = segment.estimated_end

            # Determine verification status based on verification report
            verification_passed = True
            if self.verification_enabled and verification_report:
                # Check if this segment failed verification
                for failed in verification_report.get('failed_verifications', []):
                    if failed['segment_index'] == i:
                        verification_passed = False
                        break

            processed_segments.append({
                "title": segment.title,
                "hook": segment.hook_quote.text,
                "context": " ".join([q.text for q in segment.supporting_quotes]),
                "conclusion": segment.conclusion_quote.text,
                "start_ref": start_ref,
                "end_ref": end_ref,
                "start_time": start_ref,  # For compatibility with DeepSeek format
                "end_time": end_ref,      # For compatibility with DeepSeek format
                "theme": segment.theme,
                "urgency_score": segment.urgency_score,
                "viral_potential": segment.viral_potential,
                "verification_passed": verification_passed
            })

        return processed_segments

    def _seconds_to_timestamp(self, seconds: float) -> str:
        """Convert seconds to HH:MM:SS format"""
        if seconds <= 0:
            return "00:00:00"

        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def enable_verification(self, enabled: bool = True):
        """Enable or disable verbatim quote verification"""
        self.verification_enabled = enabled
        print(f"Verbatim quote verification {'enabled' if enabled else 'disabled'}")

    def get_verification_report(self, segments: List[MilitantJournalismSegment],
                             original_transcript: str) -> Dict[str, Any]:
        """Get detailed verification report for segments"""
        return self.quote_extractor.verify_quotes_authenticity(segments, original_transcript)