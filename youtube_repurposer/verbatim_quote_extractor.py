#!/usr/bin/env python3
"""
Verbatim Quote Extractor for MintPress News
Ensures exact quotes and prevents hallucination in content repurposing
"""

import re
from typing import List, Dict, Any, Tuple, Optional, TypedDict
from dataclasses import dataclass
from datetime import datetime
import hashlib

class VerificationReport(TypedDict):
    total_segments: int
    verified_segments: int
    failed_verifications: List[Dict[str, Any]]
    authenticity_score: float

class ExportData(TypedDict):
    segments: List[Dict[str, Any]]
    metadata: Dict[str, Any]

@dataclass
class VerbatimQuote:
    """Represents an exact quote from the original transcript"""
    text: str
    start_time: str
    end_time: str
    speaker: Optional[str] = None
    context_before: str = ""
    context_after: str = ""
    quote_hash: str = ""

    def __post_init__(self):
        if not self.quote_hash:
            self.quote_hash = hashlib.md5(self.text.encode()).hexdigest()[:12]

@dataclass
class MilitantJournalismSegment:
    """Segment optimized for anti-war, anti-capitalism, anti-corruption content"""
    title: str
    hook_quote: VerbatimQuote  # Strong attention-grabbing quote
    supporting_quotes: List[VerbatimQuote]  # Context reinforcing the hook
    conclusion_quote: VerbatimQuote  # Powerful closing statement
    theme: str  # anti-war, anti-capitalism, anti-corruption, etc.
    urgency_score: float  # 0-1 scale for how urgent/important the content is
    viral_potential: str  # high, medium, low
    estimated_start: str
    estimated_end: str
    total_duration: int  # in seconds

class VerbatimQuoteExtractor:
    """Extracts verbatim quotes ensuring no hallucination"""

    def __init__(self):
        self.militant_keywords = {
            'anti_war': [
                'war', 'military', 'invasion', 'bombing', 'conflict', 'troops',
                'weapons', 'defense', 'pentagon', 'nato', 'intervention',
                'occupation', 'sanctions', 'regime change', 'imperialism'
            ],
            'anti_capitalism': [
                'capitalism', 'corporate', 'profit', 'exploitation', 'inequality',
                'wealth', 'billionaire', 'monopoly', 'privatization', 'neoliberal',
                'wall street', 'banks', 'finance', 'greed', 'class struggle'
            ],
            'anti_corruption': [
                'corruption', 'bribery', 'lobbying', 'scandal', 'cover-up',
                'transparency', 'accountability', 'whistleblower', 'fraud',
                'conspiracy', 'collusion', 'influence peddling', 'cronyism'
            ]
        }

        self.hook_patterns = [
            r'\b(?:shocking|unbelievable|exposed|revealed|hidden|secret|truth|lies)\b',
            r'\b(?:they don\'t want you to know|mainstream media won\'t tell you)\b',
            r'\b(?:breaking|urgent|critical|devastating|explosive)\b',
            r'\b(?:follow the money|cui bono|who benefits)\b'
        ]

    def extract_timestamps_from_transcript(self, transcript: str) -> List[Tuple[str, str, str]]:
        """Extract text with timestamps from VTT, SRT, or custom format"""
        segments = []

        # Custom format pattern: [HH:MM:SS:FF - HH:MM:SS:FF]
        custom_pattern = r'\[(\d{2}:\d{2}:\d{2}:\d{2}) - (\d{2}:\d{2}:\d{2}:\d{2})\]\n(?:Speaker \d+\n)?([^\[]+?)(?=\n\[|$)'
        custom_matches = re.findall(custom_pattern, transcript, re.MULTILINE | re.DOTALL)

        for start, end, text in custom_matches:
            # Convert HH:MM:SS:FF to HH:MM:SS.mmm (assuming 30fps, so FF/30 * 1000)
            start_parts = start.split(':')
            end_parts = end.split(':')

            start_ms = int(start_parts[3]) * 1000 // 30  # Convert frames to milliseconds
            end_ms = int(end_parts[3]) * 1000 // 30

            start_vtt = f"{start_parts[0]}:{start_parts[1]}:{start_parts[2]}.{start_ms:03d}"
            end_vtt = f"{end_parts[0]}:{end_parts[1]}:{end_parts[2]}.{end_ms:03d}"

            clean_text = re.sub(r'\s+', ' ', text).strip()  # Normalize whitespace
            if clean_text:
                segments.append((start_vtt, end_vtt, clean_text))

        # VTT format pattern
        if not segments:
            vtt_pattern = r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})\n([^\n]+(?:\n[^\n]+)*?)(?=\n\n|\n\d{2}:|$)'
            vtt_matches = re.findall(vtt_pattern, transcript, re.MULTILINE | re.DOTALL)

            for start, end, text in vtt_matches:
                clean_text = re.sub(r'<[^>]+>', '', text).strip()  # Remove HTML tags
                clean_text = re.sub(r'\n+', ' ', clean_text)  # Replace newlines with spaces
                if clean_text:
                    segments.append((start, end, clean_text))

        # SRT format pattern
        if not segments:
            srt_pattern = r'\d+\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n([^\n]+(?:\n[^\n]+)*?)(?=\n\n|\n\d+\n|$)'
            srt_matches = re.findall(srt_pattern, transcript, re.MULTILINE | re.DOTALL)

            for start, end, text in srt_matches:
                clean_text = re.sub(r'<[^>]+>', '', text).strip()
                clean_text = re.sub(r'\n+', ' ', clean_text)
                if clean_text:
                    # Convert SRT time format to VTT format
                    start_vtt = start.replace(',', '.')
                    end_vtt = end.replace(',', '.')
                    segments.append((start_vtt, end_vtt, clean_text))

        return segments

    def identify_militant_themes(self, text: str) -> List[str]:
        """Identify militant journalism themes in text"""
        themes = []
        text_lower = text.lower()

        for theme, keywords in self.militant_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                themes.append(theme)

        return themes

    def score_hook_potential(self, text: str) -> float:
        """Score how likely a quote is to grab attention and go viral"""
        score = 0.0
        text_lower = text.lower()

        # High-impact words and phrases
        impact_words = [
            'shocking', 'exposed', 'revealed', 'secret', 'hidden', 'truth',
            'corruption', 'scandal', 'cover-up', 'lies', 'deception',
            'urgent', 'critical', 'dangerous', 'threat', 'crisis', 'concerning',
            'alarming', 'unprecedented', 'serious', 'breach', 'violation',
            'manipulate', 'undermine', 'democracy', 'accountability', 'transparency',
            'investigate', 'challenge', 'demand', 'fight', 'action', 'movement'
        ]

        # Emotional intensity words
        emotion_words = [
            'outrageous', 'unacceptable', 'disgusting', 'appalling',
            'must', 'need', 'should', 'cannot', 'will not', 'refuse'
        ]

        # Question words that create engagement
        question_indicators = ['?', 'why', 'how', 'what', 'when', 'where', 'who']

        for word in impact_words:
            if word in text_lower:
                score += 0.15

        for word in emotion_words:
            if word in text_lower:
                score += 0.1

        for indicator in question_indicators:
            if indicator in text_lower:
                score += 0.05

        # Bonus for longer, more substantive quotes
        word_count = len(text.split())
        if word_count > 20:
            score += 0.1
        if word_count > 40:
            score += 0.1

        # Bonus for quotes that mention specific examples or numbers
        if any(char.isdigit() for char in text):
            score += 0.05

        return min(score, 1.0)

    def extract_verbatim_quotes(self, transcript: str, min_quote_length: int = 10) -> List[VerbatimQuote]:
        """Extract verbatim quotes with exact timestamps"""
        segments = self.extract_timestamps_from_transcript(transcript)
        quotes = []

        print(f"DEBUG: Extracted {len(segments)} transcript segments")

        for i, (start, end, text) in enumerate(segments):
            if len(text) >= min_quote_length:
                # Get context from surrounding segments
                context_before = ""
                context_after = ""

                if i > 0:
                    context_before = segments[i-1][2]
                if i < len(segments) - 1:
                    context_after = segments[i+1][2]

                quote = VerbatimQuote(
                    text=text,
                    start_time=start,
                    end_time=end,
                    context_before=context_before,
                    context_after=context_after
                )
                quotes.append(quote)

        print(f"DEBUG: Created {len(quotes)} verbatim quotes")
        return quotes

    def create_militant_segments(self, quotes: List[VerbatimQuote], max_segments: int = 5, min_duration: int = 60, max_duration: int = 180) -> List[MilitantJournalismSegment]:
        """Create segments optimized for militant journalism with speech-based duration (3 words/second)"""
        if not quotes:
            print("DEBUG: No quotes provided for segment creation")
            return []

        print(f"DEBUG: Creating segments from {len(quotes)} quotes")

        segments = []
        WORDS_PER_SECOND = 3  # Speech rate assumption

        # Group quotes by theme, but be more inclusive
        themed_quotes: Dict[str, List[VerbatimQuote]] = {}
        unthemed_quotes = []

        for quote in quotes:
            themes = self.identify_militant_themes(quote.text)
            if themes:
                for theme in themes:
                    if theme not in themed_quotes:
                        themed_quotes[theme] = []
                    themed_quotes[theme].append(quote)
            else:
                unthemed_quotes.append(quote)

        # If we don't have enough themed quotes, create a general theme
        if not themed_quotes or sum(len(quotes) for quotes in themed_quotes.values()) < len(quotes) * 0.5:
            themed_quotes['general_content'] = unthemed_quotes

        print(f"DEBUG: Found {len(themed_quotes)} themes: {list(themed_quotes.keys())}")
        print(f"DEBUG: Unthemed quotes: {len(unthemed_quotes)}")

        # Create segments for each theme
        for theme, theme_quotes in themed_quotes.items():
            print(f"DEBUG: Processing theme '{theme}' with {len(theme_quotes)} quotes")

            if len(theme_quotes) < 2:  # Need at least 2 quotes to make a segment
                print(f"DEBUG: Skipping theme '{theme}' - insufficient quotes ({len(theme_quotes)} < 2)")
                continue

            # Score quotes for hook potential
            scored_quotes = [(quote, self.score_hook_potential(quote.text)) for quote in theme_quotes]
            scored_quotes.sort(key=lambda x: x[1], reverse=True)

            # Select best hook
            hook_quote = scored_quotes[0][0]
            print(f"DEBUG: Selected hook quote: '{hook_quote.text[:50]}...'")

            # Select conclusion quote
            if len(theme_quotes) >= 3:
                # Use chronologically last quote as conclusion
                remaining_quotes = [q for q, _s in scored_quotes[1:]]
                remaining_quotes.sort(key=lambda q: self._time_to_seconds(q.start_time))
                conclusion_quote = remaining_quotes[-1]
                available_supporting = [q for q in remaining_quotes if q != conclusion_quote]
            elif len(theme_quotes) == 2:
                # Use the second quote as conclusion
                conclusion_quote = scored_quotes[1][0]
                available_supporting = []
            else:
                # Only one quote - use it for both hook and conclusion
                conclusion_quote = hook_quote
                available_supporting = []

            # Start with minimal supporting quotes and build up based on word count
            supporting_quotes = []

            # Build segment content based on speech duration requirements
            current_quotes = [hook_quote, conclusion_quote]
            current_word_count = self._count_words_in_quotes(current_quotes)
            current_speech_duration = current_word_count / WORDS_PER_SECOND

            print(f"DEBUG: Initial {theme}: {current_word_count} words = {current_speech_duration:.1f}s speech (target: {min_duration}-{max_duration}s)")

            # Add supporting quotes until we reach optimal duration
            # available_supporting already defined above based on number of quotes

            # First, add all available supporting quotes for this theme
            for support_quote in available_supporting:
                test_quotes = current_quotes + [support_quote]
                test_word_count = self._count_words_in_quotes(test_quotes)
                test_speech_duration = test_word_count / WORDS_PER_SECOND

                print(f"DEBUG: Testing quote addition - words: {test_word_count}, duration: {test_speech_duration:.1f}s")

                if test_speech_duration <= max_duration:
                    supporting_quotes.append(support_quote)
                    current_quotes = test_quotes
                    current_word_count = test_word_count
                    current_speech_duration = test_speech_duration
                    print(f"DEBUG: Added supporting quote, now {current_speech_duration:.1f}s")
                else:
                    print(f"DEBUG: Stopping at {current_speech_duration:.1f}s to avoid exceeding {max_duration}s limit")
                    break

            # If still under minimum, add quotes from other themes to reach target
            if current_speech_duration < min_duration:
                print(f"DEBUG: Segment too short ({current_speech_duration:.1f}s < {min_duration}s), adding quotes from other themes...")

                # Get quotes from other themes that aren't already used
                used_quotes = [hook_quote] + supporting_quotes + [conclusion_quote]
                other_quotes = [q for q in quotes if q not in used_quotes]

                # Sort by hook potential to get the best additional quotes
                other_scored = [(q, self.score_hook_potential(q.text)) for q in other_quotes]
                other_scored.sort(key=lambda x: x[1], reverse=True)

                for support_quote, score in other_scored:
                    test_quotes = current_quotes + [support_quote]
                    test_word_count = self._count_words_in_quotes(test_quotes)
                    test_speech_duration = test_word_count / WORDS_PER_SECOND

                    if test_speech_duration <= max_duration:
                        supporting_quotes.append(support_quote)
                        current_quotes = test_quotes
                        current_word_count = test_word_count
                        current_speech_duration = test_speech_duration

                        print(f"DEBUG: Added cross-theme quote (score: {score:.2f}), now {current_speech_duration:.1f}s")

                        if current_speech_duration >= min_duration:
                            print(f"DEBUG: Reached minimum duration: {current_word_count} words = {current_speech_duration:.1f}s")
                            break
                    else:
                        print(f"DEBUG: Would exceed max duration, stopping at {current_speech_duration:.1f}s")
                        break

            # Calculate timing based on the selected quotes
            all_segment_quotes = [hook_quote] + supporting_quotes + [conclusion_quote]
            start_times = [self._time_to_seconds(q.start_time) for q in all_segment_quotes]
            end_times = [self._time_to_seconds(q.end_time) for q in all_segment_quotes]

            final_start = min(start_times)
            final_end = max(end_times)
            actual_duration = final_end - final_start

            estimated_start = self._seconds_to_time(final_start)
            estimated_end = self._seconds_to_time(final_end)

            print(f"DEBUG: Final {theme}: {len(all_segment_quotes)} quotes, {current_word_count} words, {current_speech_duration:.1f}s speech, {actual_duration:.1f}s timeline")

            # Create segment
            segment = MilitantJournalismSegment(
                title=f"{theme.replace('_', ' ').title()} Exposé",
                hook_quote=hook_quote,
                supporting_quotes=supporting_quotes,
                conclusion_quote=conclusion_quote,
                theme=theme,
                urgency_score=scored_quotes[0][1],  # Use hook score as urgency
                viral_potential="high" if scored_quotes[0][1] > 0.7 else "medium" if scored_quotes[0][1] > 0.4 else "low",
                estimated_start=estimated_start,
                estimated_end=estimated_end,
                total_duration=int(current_speech_duration)  # Use speech-based duration
            )

            segments.append(segment)
            print(f"DEBUG: Created segment '{segment.title}' with {len(all_segment_quotes)} quotes")

        # Sort by urgency and return top segments
        segments.sort(key=lambda x: x.urgency_score, reverse=True)
        print(f"DEBUG: Created {len(segments)} total segments")
        return segments[:max_segments]

    def _count_words_in_quotes(self, quotes: List[VerbatimQuote]) -> int:
        """Count total words in a list of quotes for speech duration calculation"""
        total_words = 0
        for quote in quotes:
            # Simple word count - split by whitespace and filter out empty strings
            words = [word for word in quote.text.split() if word.strip()]
            total_words += len(words)
        return total_words

    def _time_to_seconds(self, time_str: str) -> float:
        """Convert time string to seconds"""
        try:
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        except (ValueError, IndexError):
            return 0.0

    def _seconds_to_time(self, seconds: float) -> str:
        """Convert seconds to time string"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{millisecs:03d}"

    def verify_quotes_authenticity(self, segments: List[MilitantJournalismSegment], original_transcript: str) -> VerificationReport:
        """Verify all quotes exist verbatim in original transcript"""
        print(f"DEBUG: Starting verification for {len(segments)} segments")

        verification_report: VerificationReport = {
            'total_segments': len(segments),
            'verified_segments': 0,
            'failed_verifications': [],
            'authenticity_score': 0.0
        }

        original_clean = re.sub(r'\s+', ' ', original_transcript.lower())
        print(f"DEBUG: Original transcript length: {len(original_transcript)} chars, cleaned: {len(original_clean)} chars")

        for i, segment in enumerate(segments):
            print(f"DEBUG: Verifying segment {i}: '{segment.title}'")
            segment_verified = True
            failed_quotes = []

            # Check all quotes in segment
            all_quotes = [segment.hook_quote] + segment.supporting_quotes + [segment.conclusion_quote]
            print(f"DEBUG: Checking {len(all_quotes)} quotes in segment {i}")

            for j, quote in enumerate(all_quotes):
                quote_clean = re.sub(r'\s+', ' ', quote.text.lower())
                print(f"DEBUG: Quote {j}: '{quote.text[:100]}...'")
                print(f"DEBUG: Quote cleaned: '{quote_clean[:100]}...'")

                if quote_clean not in original_clean:
                    segment_verified = False
                    failed_quotes.append(quote.text[:50] + "...")
                    print(f"DEBUG: Quote {j} FAILED verification")
                else:
                    print(f"DEBUG: Quote {j} PASSED verification")

            if segment_verified:
                verification_report['verified_segments'] += 1
                print(f"DEBUG: Segment {i} VERIFIED")
            else:
                verification_report['failed_verifications'].append({
                    'segment_index': i,
                    'segment_title': segment.title,
                    'failed_quotes': failed_quotes
                })
                print(f"DEBUG: Segment {i} FAILED verification - {len(failed_quotes)} failed quotes")

        verification_report['authenticity_score'] = verification_report['verified_segments'] / len(segments) if segments else 0
        print(f"DEBUG: Verification complete - {verification_report['verified_segments']}/{verification_report['total_segments']} verified ({verification_report['authenticity_score']:.2%})")

        return verification_report

    def export_segments_for_editing(self, segments: List[MilitantJournalismSegment]) -> ExportData:
        """Export segments in format suitable for video editing"""
        export_data: ExportData = {
            'metadata': {
                'created_at': datetime.now().isoformat(),
                'total_segments': len(segments),
                'extractor_version': '1.0.0'
            },
            'segments': []
        }

        for i, segment in enumerate(segments):
            segment_data = {
                'id': i + 1,
                'title': segment.title,
                'theme': segment.theme,
                'urgency_score': segment.urgency_score,
                'viral_potential': segment.viral_potential,
                'estimated_start': segment.estimated_start,
                'estimated_end': segment.estimated_end,
                'total_duration': segment.total_duration,
                'quotes': {
                    'hook': {
                        'text': segment.hook_quote.text,
                        'start_time': segment.hook_quote.start_time,
                        'end_time': segment.hook_quote.end_time,
                        'hash': segment.hook_quote.quote_hash
                    },
                    'supporting': [
                        {
                            'text': q.text,
                            'start_time': q.start_time,
                            'end_time': q.end_time,
                            'hash': q.quote_hash
                        } for q in segment.supporting_quotes
                    ],
                    'conclusion': {
                        'text': segment.conclusion_quote.text,
                        'start_time': segment.conclusion_quote.start_time,
                        'end_time': segment.conclusion_quote.end_time,
                        'hash': segment.conclusion_quote.quote_hash
                    }
                }
            }
            export_data['segments'].append(segment_data)

        return export_data

# Example usage and testing
if __name__ == "__main__":
    extractor = VerbatimQuoteExtractor()

    # Test with sample VTT content
    sample_vtt = """
WEBVTT

00:00:01.000 --> 00:00:05.000
The military industrial complex profits from endless wars.

00:00:05.000 --> 00:00:10.000
They don't want you to know how much money they make from conflict.

00:00:10.000 --> 00:00:15.000
Every bomb dropped means more profit for defense contractors.

00:00:15.000 --> 00:00:20.000
This is why peace is never profitable for them.

00:00:20.000 --> 00:00:25.000
We must expose this corruption and demand accountability.
"""

    quotes = extractor.extract_verbatim_quotes(sample_vtt)
    segments = extractor.create_militant_segments(quotes)
    verification = extractor.verify_quotes_authenticity(segments, sample_vtt)

    print(f"Extracted {len(quotes)} quotes")
    print(f"Created {len(segments)} segments")
    print(f"Authenticity score: {verification['authenticity_score']:.2f}")