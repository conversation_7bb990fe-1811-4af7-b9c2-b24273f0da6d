# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import Required, TypeAlias, TypedDict

from ...graders.multi_grader_param import MultiGraderParam
from ...graders.python_grader_param import PythonGraderParam
from ...graders.score_model_grader_param import ScoreModelGraderParam
from ...graders.string_check_grader_param import StringCheckGraderParam
from ...graders.text_similarity_grader_param import TextSimilarityGraderParam

__all__ = ["GraderRunParams", "Grader"]


class GraderRunParams(TypedDict, total=False):
    grader: Required[Grader]
    """The grader used for the fine-tuning job."""

    model_sample: Required[str]
    """The model sample to be evaluated.

    This value will be used to populate the `sample` namespace. See
    [the guide](https://platform.openai.com/docs/guides/graders) for more details.
    The `output_json` variable will be populated if the model sample is a valid JSON
    string.
    """

    item: object
    """The dataset item provided to the grader.

    This will be used to populate the `item` namespace. See
    [the guide](https://platform.openai.com/docs/guides/graders) for more details.
    """


Grader: TypeAlias = Union[
    StringCheckGraderParam, TextSimilarityGraderParam, PythonGraderParam, ScoreModelGraderParam, MultiGraderParam
]
