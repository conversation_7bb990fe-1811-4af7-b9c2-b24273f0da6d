import os
import subprocess
import threading
from flask import Flask, render_template, request, redirect, url_for, send_from_directory, jsonify
from typing import Optional
from werkzeug.utils import secure_filename
from thumbnail_generator import generate_thumbnail
from progress_tracker import ProgressTracker
from rag_processor import TranscriptProcessor  # New RAG processor
from whisperx_transcriber import get_transcriber
from deepseek_config import create_deepseek_client
import asyncio
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import our new template system
from examples.youtube_repurposer_app import YouTubeRepurposerApp

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['SHORTS_FOLDER'] = 'static/shorts'
app.config['THUMBNAILS_FOLDER'] = 'static/thumbnails'
app.config['TRANSCRIPTS_FOLDER'] = 'static/transcripts'
app.config['ALLOWED_EXTENSIONS'] = {'mp4', 'mov', 'avi', 'mkv', 'txt', 'vtt'}
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB
app.secret_key = os.urandom(24)

# Ensure directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['SHORTS_FOLDER'], exist_ok=True)
os.makedirs(app.config['THUMBNAILS_FOLDER'], exist_ok=True)
os.makedirs(app.config['TRANSCRIPTS_FOLDER'], exist_ok=True)

# Initialize progress tracker
tracker = ProgressTracker()

# Initialize RAG processor
transcript_processor = TranscriptProcessor()

# Initialize DeepSeek client for content analysis
deepseek_client = create_deepseek_client()
if deepseek_client:
    print("✅ DeepSeek R1 client initialized successfully")
else:
    print("⚠️ DeepSeek R1 client not available - using fallback analysis")

# Initialize enhanced template-based application
enhanced_app: Optional[YouTubeRepurposerApp] = None
try:
    enhanced_app = YouTubeRepurposerApp({
        'log_level': 'INFO',
        'cache_enabled': True,
        'enable_rag': True,
        'enable_context_optimization': True,
        'enable_web_crawling': False  # Disabled for web deployment
    })
    print("✅ Enhanced YouTube Repurposer initialized successfully")
except Exception as e:
    print(f"⚠️ Enhanced app initialization failed: {e}")

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def convert_deepseek_segments(deepseek_segments):
    """Convert DeepSeek R1 segment format to expected format"""
    converted_segments = []

    for segment in deepseek_segments:
        # Handle both DeepSeek format and fallback format
        converted_segment = {
            'title': segment.get('title', 'Untitled Segment'),
            'hook': segment.get('hook', ''),
            'context': segment.get('context', ''),
            'conclusion': segment.get('conclusion', ''),
            'start_ref': segment.get('start_time', segment.get('estimated_start', '00:00')),
            'end_ref': segment.get('end_time', segment.get('estimated_end', '01:00')),
            'engagement_score': segment.get('engagement_score', 0.5),
            'topics': segment.get('topics', []),
            'complexity_level': segment.get('complexity_level', 'intermediate'),
            'viral_potential': segment.get('viral_potential', 'medium')
        }
        converted_segments.append(converted_segment)

    return converted_segments

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return redirect(request.url)

    file = request.files['file']
    transcript_text = request.form.get('transcript', '')

    # Handle transcript file upload
    transcript_file = request.files.get('transcript_file')
    transcript_path = None

    if transcript_file and allowed_file(transcript_file.filename):
        filename = secure_filename(transcript_file.filename)
        transcript_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], filename)
        transcript_file.save(transcript_path)
        with open(transcript_path, 'r') as f:
            transcript_text = f.read()

        # Convert VTT to plain text if needed
        if filename.lower().endswith('.vtt'):
            from vtt_utils import vtt_to_text
            transcript_text = vtt_to_text(transcript_text)

    if file.filename == '' and not transcript_text:
        return redirect(request.url)

    # Create tracking job
    job_id = tracker.create_job()

    # Save video file in main thread before background processing
    filepath = None
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

    # Start processing in background thread with file path
    threading.Thread(target=process_content, args=(job_id, filepath, transcript_text)).start()

    return redirect(url_for('processing', job_id=job_id))

def process_content(job_id, file_path, transcript_text):
    """Process content in background thread"""
    # Process video file if uploaded
    filepath = None
    if file_path:
        # File was already saved in the main thread
        filepath = file_path
        tracker.update_job(job_id, 'processing', 'File uploaded', 1, 10)
    else:
        tracker.update_job(job_id, 'processing', 'Processing transcript', 1, 10)

    # Generate transcript if not provided and video file exists
    if not transcript_text and filepath:
        try:
            tracker.update_job(job_id, 'processing', 'Transcribing audio with WhisperX', 2, 10)

            # Initialize WhisperX transcriber
            transcriber = get_transcriber(
                model_size="base",  # Use base model for speed
                enable_diarization=False,  # Disable speaker identification for now
                device="auto"  # Auto-detect best device
            )

            # Transcribe the video file
            transcription_result = transcriber.transcribe_file(filepath)
            transcript_text = transcription_result.full_text

            # Save transcription to file for future reference
            base_name = os.path.splitext(os.path.basename(filepath))[0]
            transcript_save_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.txt")
            with open(transcript_save_path, 'w', encoding='utf-8') as f:
                f.write(transcript_text)

            # Also save as VTT for subtitle use (if transcriber supports it)
            vtt_save_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.vtt")
            try:
                transcriber.export_to_vtt(transcription_result, vtt_save_path)
            except AttributeError:
                # Mock transcriber doesn't support VTT export
                print("VTT export not supported by current transcriber")

            tracker.update_job(job_id, 'processing', f'Transcription complete ({transcription_result.word_count} words)', 3, 10)

        except Exception as e:
            tracker.update_job(job_id, 'error', f'Transcription failed: {str(e)}', 2, 10)
            return

    # Process transcript
    shorts = []
    if transcript_text:
        # Analyze transcript using DeepSeek R1 or fallback
        tracker.update_job(job_id, 'processing', 'Analyzing transcript with DeepSeek R1', 4, 10)

        if deepseek_client:
            # Use DeepSeek R1 for advanced analysis
            try:
                analysis_result = asyncio.run(deepseek_client.analyze_long_transcript(transcript_text, "comprehensive"))

                if 'error' not in analysis_result:
                    segments = analysis_result.get('top_segments', analysis_result.get('segments', []))
                    print(f"DEBUG: Raw DeepSeek segments: {segments}")
                    # Convert DeepSeek format to expected format
                    segments = convert_deepseek_segments(segments)
                    print(f"DEBUG: Converted segments: {segments}")
                else:
                    print(f"DeepSeek analysis error: {analysis_result['error']}")
                    # Fallback to RAG processor
                    segments = transcript_processor.process_transcript(transcript_text)
                    print(f"DEBUG: RAG fallback segments: {segments}")
            except Exception as e:
                print(f"DeepSeek analysis failed: {e}")
                # Fallback to RAG processor
                segments = transcript_processor.process_transcript(transcript_text)
                print(f"DEBUG: RAG fallback segments after exception: {segments}")
        else:
            # Use RAG processor as fallback
            segments = transcript_processor.process_transcript(transcript_text)
            print(f"DEBUG: RAG processor segments (no DeepSeek): {segments}")

        tracker.update_job(job_id, 'processing', f'Identified {len(segments)} segments', 5, 10)

        if segments:
            if filepath:
                base_name = os.path.splitext(os.path.basename(filepath))[0]
                shorts = process_video(job_id, filepath, base_name, segments)
            else:
                # Handle transcript-only case
                for i, segment in enumerate(segments):
                    tracker.update_segment(job_id, i, 'processing', 'Extracting metadata')
                    segment_data = {
                        "title": segment['title'],
                        "hook": segment['hook'],
                        "context": segment['context'],
                        "conclusion": segment['conclusion'],
                        "start_ref": segment.get('start_ref', ''),
                        "end_ref": segment.get('end_ref', ''),
                        "filename": None,
                        "thumbnail": None,
                        "verification_passed": segment.get('verification_passed', False),
                        "theme": segment.get('theme', ''),
                        "urgency_score": segment.get('urgency_score', 0),
                        "viral_potential": segment.get('viral_potential', 0),
                        "full_transcript": segment.get('full_transcript', transcript_text),  # Include full transcript
                        "supporting_quotes": segment.get('supporting_quotes', [])  # Include supporting quotes if available
                    }
                    shorts.append(segment_data)
                    tracker.update_segment(job_id, i, 'completed', 'Segment processed', segment_data)

    # Results are already tracked by the tracker system
    # No need to store in session as we're in a background thread
    tracker.update_job(job_id, 'completed', 'Processing complete', 10, 10)

def process_video(job_id, input_path, base_name, segments):
    """Create Shorts based on identified segments"""
    generated_shorts = []
    total_segments = len(segments)

    for i, segment in enumerate(segments):
        # Update segment status
        tracker.update_segment(job_id, i, 'processing', 'Starting video processing')

        # Create output path
        output_path = os.path.join(app.config['SHORTS_FOLDER'], f"{base_name}_short_{i}.mp4")

        # Calculate duration from timestamps
        start_time = segment['start_ref']
        end_time = segment['end_ref']

        # Convert time to seconds (handles both MM:SS and HH:MM:SS formats)
        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0  # Invalid format, return 0

        duration_seconds = time_to_seconds(end_time) - time_to_seconds(start_time)

        # FFmpeg command with actual timestamps
        cmd = [
            'ffmpeg',
            '-ss', start_time,  # Use actual start time
            '-i', input_path,
            '-t', str(duration_seconds),  # Use calculated duration
            '-vf', "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,format=yuv420p",
            '-c:v', 'libx264',
            '-profile:v', 'main',
            '-crf', '23',
            '-preset', 'fast',
            '-c:a', 'aac',
            '-b:a', '128k',
            '-movflags', '+faststart',
            output_path
        ]

        try:
            tracker.update_segment(job_id, i, 'processing', 'Generating video clip')
            subprocess.run(cmd, check=True)

            # Generate thumbnail
            tracker.update_segment(job_id, i, 'processing', 'Generating thumbnail')
            thumbnail_path = os.path.join(app.config['THUMBNAILS_FOLDER'], f"{base_name}_thumb_{i}.jpg")
            thumbnail_success = generate_thumbnail(output_path, thumbnail_path, segment['title'], segment['hook'])

            # Add metadata to track
            generated_shorts.append({
                "filename": os.path.basename(output_path),
                "thumbnail": os.path.basename(thumbnail_path) if thumbnail_success else None,
                "title": segment['title'],
                "hook": segment['hook'],
                "context": segment['context'],
                "conclusion": segment['conclusion'],
                "verification_passed": segment.get('verification_passed', False),
                "theme": segment.get('theme', ''),
                "urgency_score": segment.get('urgency_score', 0),
                "viral_potential": segment.get('viral_potential', 0),
                "start_ref": segment.get('start_ref', ''),
                "end_ref": segment.get('end_ref', '')
            })

            tracker.update_segment(job_id, i, 'completed', 'Segment completed')

            # Update overall progress
            current_progress = 4 + int((i + 1) / total_segments * 6)
            tracker.update_job(job_id, 'processing', f'Processed {i+1}/{total_segments} segments', current_progress, 10)

        except subprocess.CalledProcessError as e:
            print(f"Error processing segment {i}: {e}")
            tracker.update_segment(job_id, i, 'failed', str(e))

    return generated_shorts

@app.route('/processing/<job_id>')
def processing(job_id):
    return render_template('processing.html', job_id=job_id)

@app.route('/progress/<job_id>')
def progress(job_id):
    progress = tracker.get_progress(job_id)
    return jsonify(progress)

@app.route('/results/<job_id>')
def results(job_id):
    """Display results for a specific job"""
    progress_data = tracker.get_progress(job_id)

    if not progress_data or progress_data.get('status') != 'completed':
        return redirect(url_for('index'))

    # Get all completed segments
    shorts = []
    for segment_id, segment_data in progress_data.get('segments', {}).items():
        if segment_data.get('status') == 'completed':
            shorts.append({
                'title': segment_data.get('title', f'Short {segment_id}'),
                'hook': segment_data.get('hook', ''),
                'context': segment_data.get('context', ''),
                'conclusion': segment_data.get('conclusion', ''),
                'start_ref': segment_data.get('start_ref', ''),
                'end_ref': segment_data.get('end_ref', ''),
                'filename': segment_data.get('filename', None),
                'thumbnail': segment_data.get('thumbnail', None),
                'verification_passed': segment_data.get('verification_passed', False),
                'theme': segment_data.get('theme', ''),
                'urgency_score': segment_data.get('urgency_score', 0),
                'viral_potential': segment_data.get('viral_potential', 0),
                'full_transcript': segment_data.get('full_transcript', ''),
                'supporting_quotes': segment_data.get('supporting_quotes', [])
            })

    return render_template('results.html', shorts=shorts, job_id=job_id)

@app.route('/api/verification/toggle', methods=['POST'])
def toggle_verification():
    """Toggle verbatim quote verification on/off"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        # Update the transcript processor verification setting
        transcript_processor.enable_verification(enabled)

        return jsonify({
            'success': True,
            'verification_enabled': enabled,
            'message': f"Verbatim quote verification {'enabled' if enabled else 'disabled'}"
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/verification/status')
def verification_status():
    """Get current verification status"""
    try:
        return jsonify({
            'verification_enabled': transcript_processor.verification_enabled,
            'extractor_available': hasattr(transcript_processor, 'quote_extractor')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Route to serve generated files
@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    # Use threaded mode to handle multiple requests
    app.run(debug=True, threaded=True, port=5002, use_reloader=False)