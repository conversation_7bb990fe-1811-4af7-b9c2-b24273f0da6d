import os
import subprocess
import threading
from flask import Flask, render_template, request, redirect, url_for, send_from_directory, jsonify
from typing import Optional
from werkzeug.utils import secure_filename
from thumbnail_generator import generate_thumbnail
from progress_tracker import ProgressTracker
from rag_processor import TranscriptProcessor  # New RAG processor
from whisperx_transcriber import get_transcriber
from deepseek_config import create_deepseek_client
import asyncio
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(".env")

# Import our new template system
from examples.youtube_repurposer_app import YouTubeRepurposerApp

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['SHORTS_FOLDER'] = 'static/shorts'
app.config['THUMBNAILS_FOLDER'] = 'static/thumbnails'
app.config['TRANSCRIPTS_FOLDER'] = 'static/transcripts'
app.config['ALLOWED_EXTENSIONS'] = {'mp4', 'mov', 'avi', 'mkv', 'txt', 'vtt'}
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB
app.secret_key = os.urandom(24)

# Ensure directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['SHORTS_FOLDER'], exist_ok=True)
os.makedirs(app.config['THUMBNAILS_FOLDER'], exist_ok=True)
os.makedirs(app.config['TRANSCRIPTS_FOLDER'], exist_ok=True)

# Initialize progress tracker
tracker = ProgressTracker()

# Initialize RAG processor
transcript_processor = TranscriptProcessor()

# Initialize DeepSeek client for content analysis
deepseek_client = create_deepseek_client()
if deepseek_client:
    print("✅ DeepSeek R1 client initialized successfully")
else:
    print("⚠️ DeepSeek R1 client not available - using fallback analysis")

# Initialize enhanced template-based application
enhanced_app: Optional[YouTubeRepurposerApp] = None
try:
    enhanced_app = YouTubeRepurposerApp({
        'log_level': 'INFO',
        'cache_enabled': True,
        'enable_rag': True,
        'enable_context_optimization': True,
        'enable_web_crawling': False  # Disabled for web deployment
    })
    print("✅ Enhanced YouTube Repurposer initialized successfully")
except Exception as e:
    print(f"⚠️ Enhanced app initialization failed: {e}")

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def convert_deepseek_segments(deepseek_segments):
    """Convert DeepSeek R1 segment format to expected format"""
    converted_segments = []

    for segment in deepseek_segments:
        # Handle both DeepSeek format and fallback format
        converted_segment = {
            'title': segment.get('title', 'Untitled Segment'),
            'hook': segment.get('hook', ''),
            'context': segment.get('context', ''),
            'conclusion': segment.get('conclusion', ''),
            'start_ref': segment.get('start_time', segment.get('estimated_start', '00:00')),
            'end_ref': segment.get('end_time', segment.get('estimated_end', '01:00')),
            'engagement_score': segment.get('engagement_score', 0.5),
            'topics': segment.get('topics', []),
            'complexity_level': segment.get('complexity_level', 'intermediate'),
            'viral_potential': segment.get('viral_potential', 'medium')
        }
        converted_segments.append(converted_segment)

    return converted_segments

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return redirect(request.url)

    file = request.files['file']
    transcript_text = request.form.get('transcript', '')

    # Handle transcript file upload
    transcript_file = request.files.get('transcript_file')
    transcript_path = None

    if transcript_file and allowed_file(transcript_file.filename):
        filename = secure_filename(transcript_file.filename)
        transcript_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], filename)
        transcript_file.save(transcript_path)
        with open(transcript_path, 'r') as f:
            transcript_text = f.read()

        # Convert VTT to plain text if needed
        if filename.lower().endswith('.vtt'):
            from vtt_utils import vtt_to_text
            transcript_text = vtt_to_text(transcript_text)

    if file.filename == '' and not transcript_text:
        return redirect(request.url)

    # Create tracking job
    job_id = tracker.create_job()

    # Save video file in main thread before background processing
    filepath = None
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

    # Start processing in background thread with file path
    threading.Thread(target=process_content, args=(job_id, filepath, transcript_text)).start()

    return redirect(url_for('processing', job_id=job_id))

def process_content(job_id, file_path, transcript_text):
    """Process content in background thread"""
    # Process video file if uploaded
    filepath = None
    if file_path:
        # File was already saved in the main thread
        filepath = file_path
        tracker.update_job(job_id, 'processing', 'File uploaded', 1, 10)
    else:
        tracker.update_job(job_id, 'processing', 'Processing transcript', 1, 10)

    # Generate transcript if not provided and video file exists
    if not transcript_text and filepath:
        try:
            tracker.update_job(job_id, 'processing', 'Transcribing audio with WhisperX', 2, 10)

            # Initialize WhisperX transcriber
            transcriber = get_transcriber(
                model_size="base",  # Use base model for speed
                enable_diarization=False,  # Disable speaker identification for now
                device="auto"  # Auto-detect best device
            )

            # Transcribe the video file
            transcription_result = transcriber.transcribe_file(filepath)
            transcript_text = transcription_result.full_text

            # Save transcription to file for future reference
            base_name = os.path.splitext(os.path.basename(filepath))[0]
            transcript_save_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.txt")
            with open(transcript_save_path, 'w', encoding='utf-8') as f:
                f.write(transcript_text)

            # Also save as VTT for subtitle use (if transcriber supports it)
            vtt_save_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.vtt")
            try:
                transcriber.export_to_vtt(transcription_result, vtt_save_path)
            except AttributeError:
                # Mock transcriber doesn't support VTT export
                print("VTT export not supported by current transcriber")

            # Save word-level timestamps if available
            word_timestamps_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_words.json")
            try:
                if hasattr(transcriber, '_word_level_result') and transcriber._word_level_result:
                    transcriber.export_word_timestamps(transcriber._word_level_result, word_timestamps_path)
                    print(f"DEBUG: Saved word-level timestamps to {word_timestamps_path}")
                else:
                    print("DEBUG: No word-level timestamps available")
            except Exception as e:
                print(f"DEBUG: Error saving word-level timestamps: {e}")

            tracker.update_job(job_id, 'processing', f'Transcription complete ({transcription_result.word_count} words)', 3, 10)

        except Exception as e:
            tracker.update_job(job_id, 'error', f'Transcription failed: {str(e)}', 2, 10)
            return

    # Process transcript
    shorts = []
    if transcript_text:
        # Analyze transcript using DeepSeek R1 or fallback
        tracker.update_job(job_id, 'processing', 'Analyzing transcript with DeepSeek R1', 4, 10)

        if deepseek_client:
            # Use DeepSeek R1 for advanced analysis
            try:
                analysis_result = asyncio.run(deepseek_client.analyze_long_transcript(transcript_text, "comprehensive"))

                if 'error' not in analysis_result:
                    segments = analysis_result.get('top_segments', analysis_result.get('segments', []))
                    print(f"DEBUG: Raw DeepSeek segments: {segments}")
                    # Convert DeepSeek format to expected format
                    segments = convert_deepseek_segments(segments)
                    print(f"DEBUG: Converted segments: {segments}")

                    # Validate and adjust timestamps if we have a video file
                    if filepath:
                        segments = validate_timestamps(segments, filepath)
                        print(f"DEBUG: Validated segments: {segments}")

                        # Content validation: ensure segments match actual transcript
                        base_name = os.path.splitext(os.path.basename(filepath))[0]
                        segments = validate_segment_content(segments, base_name, transcript_text)
                        print(f"DEBUG: Content-validated segments: {len(segments)} segments")

                        # Duration enforcement: split segments that are too long
                        segments = enforce_duration_limits(segments)
                        print(f"DEBUG: Duration-enforced segments: {len(segments)} segments")
                else:
                    print(f"DeepSeek analysis error: {analysis_result['error']}")
                    # Fallback to RAG processor
                    segments = transcript_processor.process_transcript(transcript_text)
                    print(f"DEBUG: RAG fallback segments: {segments}")
            except Exception as e:
                print(f"DeepSeek analysis failed: {e}")
                # Fallback to RAG processor
                segments = transcript_processor.process_transcript(transcript_text)
                print(f"DEBUG: RAG fallback segments after exception: {segments}")
        else:
            # Use RAG processor as fallback
            segments = transcript_processor.process_transcript(transcript_text)
            print(f"DEBUG: RAG processor segments (no DeepSeek): {segments}")

        tracker.update_job(job_id, 'processing', f'Identified {len(segments)} segments', 5, 10)

        if segments:
            if filepath:
                base_name = os.path.splitext(os.path.basename(filepath))[0]
                print(f"DEBUG: Starting video processing for {len(segments)} segments")
                try:
                    shorts = process_video(job_id, filepath, base_name, segments)
                    print(f"DEBUG: Video processing completed, generated {len(shorts)} shorts")
                except Exception as e:
                    print(f"ERROR: Video processing failed: {e}")
                    import traceback
                    traceback.print_exc()
                    tracker.update_job(job_id, 'error', f'Video processing failed: {str(e)}', 10, 10)
                    return
            else:
                # Handle transcript-only case
                for i, segment in enumerate(segments):
                    tracker.update_segment(job_id, i, 'processing', 'Extracting metadata')
                    segment_data = {
                        "title": segment['title'],
                        "hook": segment['hook'],
                        "context": segment['context'],
                        "conclusion": segment['conclusion'],
                        "start_ref": segment.get('start_ref', ''),
                        "end_ref": segment.get('end_ref', ''),
                        "filename": None,
                        "thumbnail": None,
                        "verification_passed": segment.get('verification_passed', False),
                        "theme": segment.get('theme', ''),
                        "urgency_score": segment.get('urgency_score', 0),
                        "viral_potential": segment.get('viral_potential', 0),
                        "full_transcript": segment.get('full_transcript', transcript_text),  # Include full transcript
                        "supporting_quotes": segment.get('supporting_quotes', [])  # Include supporting quotes if available
                    }
                    shorts.append(segment_data)
                    tracker.update_segment(job_id, i, 'completed', 'Segment processed', segment_data)

    # Results are already tracked by the tracker system
    # No need to store in session as we're in a background thread
    tracker.update_job(job_id, 'completed', 'Processing complete', 10, 10)

def extract_transcript_segment(full_transcript, start_time, end_time, base_name=None):
    """Extract transcript text for a specific time segment using WhisperX word-level timestamps"""
    try:
        # First priority: Use word-level timestamps for maximum precision
        if base_name:
            word_timestamps_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_words.json")
            if os.path.exists(word_timestamps_path):
                result = extract_from_word_timestamps(word_timestamps_path, start_time, end_time)
                if result:
                    return result

        # Second priority: Use VTT timestamps
        if base_name:
            vtt_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.vtt")
            if os.path.exists(vtt_path):
                result = extract_from_vtt(vtt_path, start_time, end_time)
                if result and result != "No transcript text found for this time range.":
                    return result

        # Fallback: estimate from full transcript
        if not full_transcript:
            return ""

        # Convert time to seconds for calculation
        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0

        start_seconds = time_to_seconds(start_time)
        end_seconds = time_to_seconds(end_time)
        duration = end_seconds - start_seconds

        # Estimate words per second (average speaking rate is ~150 words/minute = 2.5 words/second)
        words = full_transcript.split()
        total_words = len(words)
        words_per_second = 2.5
        estimated_words = int(duration * words_per_second)

        # Extract a reasonable portion of text
        start_word = min(int(start_seconds * words_per_second), total_words - estimated_words)
        end_word = min(start_word + estimated_words, total_words)

        segment_text = ' '.join(words[start_word:end_word])
        return segment_text if segment_text else full_transcript[:500] + "..."

    except Exception as e:
        print(f"Error extracting transcript segment: {e}")
        return full_transcript[:500] + "..." if full_transcript else ""

def extract_from_word_timestamps(word_timestamps_path, start_time, end_time):
    """Extract text from word-level timestamps JSON file with maximum precision"""
    try:
        import json

        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0

        start_seconds = time_to_seconds(start_time)
        end_seconds = time_to_seconds(end_time)

        with open(word_timestamps_path, 'r', encoding='utf-8') as f:
            word_data = json.load(f)

        extracted_words = []

        # Go through each segment and its words
        for segment in word_data.get("segments", []):
            for word_info in segment.get("words", []):
                word_start = word_info.get("start", 0)
                word_end = word_info.get("end", 0)
                word_text = word_info.get("word", "").strip()

                # Check if this word overlaps with our target time range
                if (word_start <= end_seconds and word_end >= start_seconds):
                    # Only include words that are substantially within our range
                    word_center = (word_start + word_end) / 2
                    if start_seconds <= word_center <= end_seconds:
                        extracted_words.append(word_text)

        result = ' '.join(extracted_words).strip()

        # Clean up the result
        if result:
            # Remove extra spaces and clean punctuation
            result = ' '.join(result.split())
            print(f"DEBUG: Word-level extraction found {len(extracted_words)} words for {start_time}-{end_time}")
            return result
        else:
            print(f"DEBUG: No words found in time range {start_time}-{end_time}")
            return ""

    except Exception as e:
        print(f"Error extracting from word timestamps: {e}")
        return ""

def extract_from_vtt(vtt_path, start_time, end_time):
    """Extract text from VTT file based on timestamps"""
    try:
        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0

        start_seconds = time_to_seconds(start_time)
        end_seconds = time_to_seconds(end_time)

        extracted_text = []

        with open(vtt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Parse VTT format
        import re
        # VTT timestamp pattern: 00:00:10.500 --> 00:00:13.200
        timestamp_pattern = r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})'

        lines = content.split('\n')
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if re.match(timestamp_pattern, line):
                match = re.match(timestamp_pattern, line)
                if match:
                    vtt_start = match.group(1).replace('.', ':').split(':')
                    vtt_end = match.group(2).replace('.', ':').split(':')

                    # Convert VTT time to seconds
                    vtt_start_sec = int(vtt_start[0]) * 3600 + int(vtt_start[1]) * 60 + int(vtt_start[2]) + int(vtt_start[3]) / 1000
                    vtt_end_sec = int(vtt_end[0]) * 3600 + int(vtt_end[1]) * 60 + int(vtt_end[2]) + int(vtt_end[3]) / 1000

                    # Check if this timestamp overlaps with our target range
                    if (vtt_start_sec <= end_seconds and vtt_end_sec >= start_seconds):
                        # Get the text for this timestamp (next non-empty line)
                        j = i + 1
                        while j < len(lines) and lines[j].strip():
                            text_line = lines[j].strip()
                            if text_line and not re.match(timestamp_pattern, text_line):
                                extracted_text.append(text_line)
                            j += 1
            i += 1

        result = ' '.join(extracted_text)
        return result if result else "No transcript text found for this time range."

    except Exception as e:
        print(f"Error extracting from VTT: {e}")
        return "Error extracting transcript from VTT file."

def validate_timestamps(segments, video_path):
    """Validate and adjust timestamps against actual video duration"""
    try:
        # Get video duration using ffprobe
        import subprocess
        import json

        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            video_info = json.loads(result.stdout)
            video_duration = float(video_info['format']['duration'])
            print(f"DEBUG: Video duration: {video_duration} seconds")

            # Validate each segment
            validated_segments = []
            for segment in segments:
                start_ref = segment.get('start_ref', '0:00')
                end_ref = segment.get('end_ref', '1:00')

                # Convert to seconds
                def time_to_seconds(t):
                    parts = t.split(':')
                    if len(parts) == 3:  # HH:MM:SS
                        h, m, s = parts
                        return int(h) * 3600 + int(m) * 60 + int(s)
                    elif len(parts) == 2:  # MM:SS
                        m, s = parts
                        return int(m) * 60 + int(s)
                    else:
                        return 0

                start_seconds = time_to_seconds(start_ref)
                end_seconds = time_to_seconds(end_ref)

                # Validate against video duration
                if start_seconds >= video_duration:
                    print(f"WARNING: Segment start time {start_ref} exceeds video duration")
                    continue  # Skip this segment

                if end_seconds > video_duration:
                    print(f"WARNING: Adjusting segment end time from {end_ref} to video end")
                    # Convert back to MM:SS format
                    end_seconds = video_duration
                    end_minutes = int(end_seconds // 60)
                    end_secs = int(end_seconds % 60)
                    segment['end_ref'] = f"{end_minutes}:{end_secs:02d}"

                if end_seconds <= start_seconds:
                    print(f"WARNING: Invalid segment duration for {start_ref} - {end_ref}")
                    continue  # Skip this segment

                validated_segments.append(segment)

            return validated_segments
        else:
            print("WARNING: Could not get video duration, using original timestamps")
            return segments

    except Exception as e:
        print(f"Error validating timestamps: {e}")
        return segments

def validate_segment_content(segments, base_name, full_transcript):
    """Validate that AI-generated segments actually match the transcript content"""
    try:
        validated_segments = []

        for i, segment in enumerate(segments):
            start_ref = segment.get('start_ref', '0:00')
            end_ref = segment.get('end_ref', '1:00')
            ai_title = segment.get('title', '')
            ai_hook = segment.get('hook', '')
            ai_context = segment.get('context', '')

            # Extract actual transcript text for this time range
            actual_transcript = extract_transcript_segment(full_transcript, start_ref, end_ref, base_name)

            if not actual_transcript or len(actual_transcript.strip()) < 10:
                print(f"WARNING: Segment {i} ({start_ref}-{end_ref}) has no meaningful transcript content")
                continue  # Skip segments with no content

            # Content validation: check if AI analysis makes sense for this content
            validation_score = calculate_content_match_score(
                actual_transcript, ai_title, ai_hook, ai_context
            )

            print(f"DEBUG: Segment {i} content validation score: {validation_score:.2f}")
            print(f"DEBUG: Actual transcript preview: {actual_transcript[:100]}...")

            # Only keep segments with reasonable content match
            if validation_score >= 0.3:  # Threshold for content relevance
                # Add the actual transcript to the segment
                segment['actual_transcript'] = actual_transcript
                segment['content_validation_score'] = validation_score
                validated_segments.append(segment)
            else:
                print(f"WARNING: Segment {i} failed content validation (score: {validation_score:.2f})")
                print(f"  AI Title: {ai_title}")
                print(f"  Actual content: {actual_transcript[:150]}...")

        print(f"Content validation: {len(validated_segments)}/{len(segments)} segments passed")
        return validated_segments

    except Exception as e:
        print(f"Error in content validation: {e}")
        return segments  # Return original segments if validation fails

def calculate_content_match_score(actual_transcript, ai_title, ai_hook, ai_context):
    """Calculate how well the AI analysis matches the actual transcript content"""
    try:
        # Convert to lowercase for comparison
        transcript_lower = actual_transcript.lower()
        title_lower = ai_title.lower()
        hook_lower = ai_hook.lower()
        context_lower = ai_context.lower()

        # Extract key words from AI analysis
        ai_words = set()
        for text in [title_lower, hook_lower, context_lower]:
            # Remove common words and extract meaningful terms
            words = text.split()
            meaningful_words = [w for w in words if len(w) > 3 and w not in {
                'this', 'that', 'with', 'from', 'they', 'them', 'were', 'been',
                'have', 'will', 'would', 'could', 'should', 'about', 'after',
                'before', 'during', 'through', 'between', 'among', 'under',
                'over', 'above', 'below', 'into', 'onto', 'upon', 'within'
            }]
            ai_words.update(meaningful_words)

        # Count matches between AI analysis and actual transcript
        matches = 0
        total_ai_words = len(ai_words)

        if total_ai_words == 0:
            return 0.0

        for word in ai_words:
            if word in transcript_lower:
                matches += 1

        # Basic word overlap score
        word_overlap_score = matches / total_ai_words

        # Bonus for length appropriateness (segments should have reasonable content)
        length_score = min(1.0, len(actual_transcript) / 100)  # Prefer segments with at least 100 chars

        # Combined score
        final_score = (word_overlap_score * 0.7) + (length_score * 0.3)

        return min(1.0, final_score)

    except Exception as e:
        print(f"Error calculating content match score: {e}")
        return 0.5  # Neutral score if calculation fails

def find_natural_break_points(segment, num_splits):
    """Find natural break points in content for intelligent splitting"""
    try:
        context = segment.get('context', '')
        if not context:
            return None

        # Look for natural break indicators
        break_indicators = [
            '. ', '? ', '! ',  # Sentence endings
            ', and ', ', but ', ', however ', ', therefore ',  # Conjunctions
            'First,', 'Second,', 'Third,', 'Next,', 'Finally,',  # Sequence words
            'Meanwhile,', 'Additionally,', 'Furthermore,',  # Transition words
        ]

        break_points = []
        for indicator in break_indicators:
            pos = context.find(indicator)
            if pos != -1:
                break_points.append(pos)

        # Sort and select best break points
        break_points.sort()
        if len(break_points) >= num_splits - 1:
            # Select evenly distributed break points
            selected_points = []
            step = len(break_points) // (num_splits - 1)
            for i in range(num_splits - 1):
                idx = min(i * step, len(break_points) - 1)
                selected_points.append(break_points[idx])
            return selected_points

        return break_points

    except Exception as e:
        print(f"Error finding natural break points: {e}")
        return None

def enforce_duration_limits(segments, max_duration=180, optimal_max=120):
    """Enforce duration limits by splitting segments that are too long"""
    try:
        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0

        def seconds_to_time(seconds):
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}:{secs:02d}"

        enforced_segments = []

        for segment in segments:
            start_ref = segment.get('start_ref', '0:00')
            end_ref = segment.get('end_ref', '1:00')

            start_seconds = time_to_seconds(start_ref)
            end_seconds = time_to_seconds(end_ref)
            duration = end_seconds - start_seconds

            print(f"DEBUG: Checking segment '{segment.get('title', 'Untitled')}' duration: {duration}s")

            # If segment is within limits, keep as-is
            if duration <= max_duration:
                enforced_segments.append(segment)
                continue

            # If segment is too long, split it intelligently
            print(f"WARNING: Segment too long ({duration}s), splitting into smaller segments")

            # Calculate number of splits needed
            target_duration = optimal_max  # 2 minutes
            num_splits = max(2, int(duration / target_duration) + 1)
            split_duration = duration / num_splits

            # Create split segments
            for i in range(num_splits):
                split_start = start_seconds + (i * split_duration)
                split_end = min(start_seconds + ((i + 1) * split_duration), end_seconds)

                # Create new segment with modified title and timing
                split_segment = segment.copy()
                split_segment['title'] = f"{segment.get('title', 'Untitled')} (Part {i+1})"
                split_segment['start_ref'] = seconds_to_time(split_start)
                split_segment['end_ref'] = seconds_to_time(split_end)

                # Adjust hook/context/conclusion for split
                if i == 0:
                    # First part keeps the hook
                    split_segment['context'] = f"Part 1: {segment.get('context', '')[:100]}..."
                    split_segment['conclusion'] = "Continued in next part..."
                elif i == num_splits - 1:
                    # Last part gets the conclusion
                    split_segment['hook'] = "Continuing from previous part..."
                    split_segment['conclusion'] = segment.get('conclusion', '')
                else:
                    # Middle parts
                    split_segment['hook'] = "Continuing from previous part..."
                    split_segment['context'] = f"Part {i+1}: {segment.get('context', '')[:100]}..."
                    split_segment['conclusion'] = "Continued in next part..."

                # Reduce engagement score slightly for split segments
                original_score = segment.get('engagement_score', 0.5)
                split_segment['engagement_score'] = max(0.3, original_score - 0.1)

                enforced_segments.append(split_segment)
                print(f"DEBUG: Created split segment {i+1}/{num_splits}: {split_segment['start_ref']}-{split_segment['end_ref']}")

        print(f"Duration enforcement: {len(segments)} → {len(enforced_segments)} segments")
        return enforced_segments

    except Exception as e:
        print(f"Error enforcing duration limits: {e}")
        return segments  # Return original segments if enforcement fails

def process_video(job_id, input_path, base_name, segments):
    """Create Shorts based on identified segments"""
    print(f"DEBUG: process_video called with {len(segments)} segments for {input_path}")
    generated_shorts = []
    total_segments = len(segments)

    # Load the full transcript for segment extraction
    transcript_path = os.path.join(app.config['TRANSCRIPTS_FOLDER'], f"{base_name}_whisperx.txt")
    full_transcript = ""
    try:
        if os.path.exists(transcript_path):
            with open(transcript_path, 'r', encoding='utf-8') as f:
                full_transcript = f.read()
            print(f"DEBUG: Loaded transcript with {len(full_transcript)} characters")
        else:
            print(f"DEBUG: No transcript file found at {transcript_path}")
    except Exception as e:
        print(f"DEBUG: Error loading transcript: {e}")

    for i, segment in enumerate(segments):
        # Update segment status
        tracker.update_segment(job_id, i, 'processing', 'Starting video processing')

        # Create output path
        output_path = os.path.join(app.config['SHORTS_FOLDER'], f"{base_name}_short_{i}.mp4")

        # Calculate duration from timestamps
        start_time = segment['start_ref']
        end_time = segment['end_ref']

        # Convert time to seconds (handles both MM:SS and HH:MM:SS formats)
        def time_to_seconds(t):
            parts = t.split(':')
            if len(parts) == 3:  # HH:MM:SS
                h, m, s = parts
                return int(h) * 3600 + int(m) * 60 + int(s)
            elif len(parts) == 2:  # MM:SS
                m, s = parts
                return int(m) * 60 + int(s)
            else:
                return 0  # Invalid format, return 0

        duration_seconds = time_to_seconds(end_time) - time_to_seconds(start_time)

        # FFmpeg command with actual timestamps
        cmd = [
            'ffmpeg',
            '-y',  # Automatically overwrite existing files
            '-ss', start_time,  # Use actual start time
            '-i', input_path,
            '-t', str(duration_seconds),  # Use calculated duration
            '-vf', "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,format=yuv420p",
            '-c:v', 'libx264',
            '-profile:v', 'main',
            '-crf', '23',
            '-preset', 'fast',
            '-c:a', 'aac',
            '-b:a', '128k',
            '-movflags', '+faststart',
            output_path
        ]

        try:
            tracker.update_segment(job_id, i, 'processing', 'Generating video clip')
            subprocess.run(cmd, check=True)

            # Generate thumbnail
            tracker.update_segment(job_id, i, 'processing', 'Generating thumbnail')
            thumbnail_path = os.path.join(app.config['THUMBNAILS_FOLDER'], f"{base_name}_thumb_{i}.jpg")
            thumbnail_success = generate_thumbnail(output_path, thumbnail_path, segment['title'], segment['hook'])

            # Extract actual transcript text for this segment
            tracker.update_segment(job_id, i, 'processing', 'Extracting transcript segment')

            # Use the validated actual transcript if available, otherwise extract fresh
            if 'actual_transcript' in segment:
                segment_transcript = segment['actual_transcript']
                print(f"DEBUG: Using pre-validated transcript for segment {i}")
            else:
                segment_transcript = extract_transcript_segment(full_transcript, start_time, end_time, base_name)
                print(f"DEBUG: Extracted fresh transcript for segment {i}")

            print(f"DEBUG: Segment {i} transcript ({len(segment_transcript)} chars): {segment_transcript[:100]}...")

            # Add metadata to track
            segment_data = {
                "filename": os.path.basename(output_path),
                "thumbnail": os.path.basename(thumbnail_path) if thumbnail_success else None,
                "title": segment['title'],
                "hook": segment['hook'],
                "context": segment['context'],
                "conclusion": segment['conclusion'],
                "verification_passed": segment.get('verification_passed', False),
                "theme": segment.get('theme', ''),
                "urgency_score": segment.get('urgency_score', 0),
                "viral_potential": segment.get('viral_potential', 0),
                "start_ref": segment.get('start_ref', ''),
                "end_ref": segment.get('end_ref', ''),
                "full_transcript": segment_transcript,  # Use actual extracted transcript
                "supporting_quotes": segment.get('supporting_quotes', []),
                "content_validation_score": segment.get('content_validation_score', 0),  # Add validation score
                "engagement_score": segment.get('engagement_score', 0),  # Add engagement score
                "topics": segment.get('topics', []),  # Add topics
                "complexity_level": segment.get('complexity_level', 'unknown')  # Add complexity
            }
            generated_shorts.append(segment_data)

            tracker.update_segment(job_id, i, 'completed', 'Segment completed', segment_data)

            # Update overall progress
            current_progress = 4 + int((i + 1) / total_segments * 6)
            tracker.update_job(job_id, 'processing', f'Processed {i+1}/{total_segments} segments', current_progress, 10)

        except subprocess.CalledProcessError as e:
            print(f"Error processing segment {i}: {e}")
            tracker.update_segment(job_id, i, 'failed', str(e))

    return generated_shorts

@app.route('/processing/<job_id>')
def processing(job_id):
    return render_template('processing.html', job_id=job_id)

@app.route('/progress/<job_id>')
def progress(job_id):
    progress = tracker.get_progress(job_id)
    return jsonify(progress)

@app.route('/results/<job_id>')
def results(job_id):
    """Display results for a specific job"""
    progress_data = tracker.get_progress(job_id)

    if not progress_data or progress_data.get('status') != 'completed':
        return redirect(url_for('index'))

    # Get all completed segments
    shorts = []
    for segment_id, segment_data in progress_data.get('segments', {}).items():
        if segment_data.get('status') == 'completed':
            shorts.append({
                'title': segment_data.get('title', f'Short {segment_id}'),
                'hook': segment_data.get('hook', ''),
                'context': segment_data.get('context', ''),
                'conclusion': segment_data.get('conclusion', ''),
                'start_ref': segment_data.get('start_ref', ''),
                'end_ref': segment_data.get('end_ref', ''),
                'filename': segment_data.get('filename', None),
                'thumbnail': segment_data.get('thumbnail', None),
                'verification_passed': segment_data.get('verification_passed', False),
                'theme': segment_data.get('theme', ''),
                'urgency_score': segment_data.get('urgency_score', 0),
                'viral_potential': segment_data.get('viral_potential', 0),
                'full_transcript': segment_data.get('full_transcript', ''),
                'supporting_quotes': segment_data.get('supporting_quotes', [])
            })

    return render_template('results.html', shorts=shorts, job_id=job_id)

@app.route('/api/verification/toggle', methods=['POST'])
def toggle_verification():
    """Toggle verbatim quote verification on/off"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        # Update the transcript processor verification setting
        transcript_processor.enable_verification(enabled)

        return jsonify({
            'success': True,
            'verification_enabled': enabled,
            'message': f"Verbatim quote verification {'enabled' if enabled else 'disabled'}"
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/verification/status')
def verification_status():
    """Get current verification status"""
    try:
        return jsonify({
            'verification_enabled': transcript_processor.verification_enabled,
            'extractor_available': hasattr(transcript_processor, 'quote_extractor')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Route to serve generated files
@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    # Use threaded mode to handle multiple requests
    app.run(debug=True, threaded=True, port=5002, use_reloader=False)