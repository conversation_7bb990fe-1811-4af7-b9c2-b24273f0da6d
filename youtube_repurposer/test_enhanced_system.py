#!/usr/bin/env python3
"""
Enhanced System Test Script

This script tests the enhanced template system to ensure all components
are working correctly before full deployment.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all enhanced components can be imported."""
    print("🔍 Testing imports...")

    try:
        # Test core imports
        from core.pocketflow import PocketFlow, LLMNode, FunctionNode, ConditionalNode
        print("✅ PocketFlow components imported successfully")

        from core.rag.enhanced_rag_processor import EnhancedRAGProcessor, VectorStore
        print("✅ Enhanced RAG components imported successfully")

        from core.context_engineering.context_optimizer import ContextOptimizer
        print("✅ Context engineering components imported successfully")

        from core.agents.content_analyzer import ContentAnalyzer
        print("✅ Content analyzer imported successfully")

        from core.app_template import ContentApplicationTemplate, create_content_app
        print("✅ App template imported successfully")

        from examples.youtube_repurposer_app import YouTubeRepurposerApp
        print("✅ YouTube repurposer app imported successfully")

        from examples.create_custom_apps import create_social_media_optimizer
        print("✅ Custom apps imported successfully")

        return True

    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False

def test_environment():
    """Test environment setup and configuration."""
    print("\n🔍 Testing environment...")

    # Check for .env file
    if os.path.exists('.env'):
        print("✅ .env file found")

        # Check for required environment variables
        from dotenv import load_dotenv
        load_dotenv(".env")

        api_keys = {
            'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY'),
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY')
        }

        for key, value in api_keys.items():
            if value and value != f'your_{key.lower()}_here':
                print(f"✅ {key} is configured")
            else:
                print(f"⚠️  {key} not configured (will use mock responses)")
    else:
        print("⚠️  .env file not found (will use defaults)")

    # Check directories
    required_dirs = ['data', 'data/vector_store', 'logs', 'uploads']
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ Directory {directory} exists")
        else:
            print(f"⚠️  Directory {directory} missing (will be created)")
            os.makedirs(directory, exist_ok=True)

    return True

def test_vector_store():
    """Test vector store initialization."""
    print("\n🔍 Testing vector store...")

    try:
        from core.rag.enhanced_rag_processor import VectorStore

        # Initialize vector store
        vector_store = VectorStore('data/vector_store/test_embeddings.db')
        print("✅ Vector store initialized")

        # Test adding a document (will use mock embeddings if no model available)
        test_doc = "This is a test document for the vector store."
        doc_id = vector_store.add_document(test_doc, {'test': True})
        print(f"✅ Test document added with ID: {doc_id}")

        # Test search (will use simple text matching if no embeddings)
        results = vector_store.search("test document", top_k=1)
        if results:
            print(f"✅ Search returned {len(results)} results")
        else:
            print("⚠️  Search returned no results (expected with mock embeddings)")

        return True

    except Exception as e:
        print(f"❌ Vector store test failed: {e}")
        return False

def test_pocketflow():
    """Test PocketFlow workflow system."""
    print("\n🔍 Testing PocketFlow...")

    try:
        from core.pocketflow import PocketFlow, FunctionNode

        # Create a simple test workflow
        workflow = PocketFlow("test_workflow")

        def test_function(context):
            return {"result": f"Processed: {context.get('input', 'no input')}"}

        workflow.add_node(FunctionNode("test_node", test_function))

        # Test execution
        result = workflow.execute({"input": "test data"})

        if result.get('test_node', {}).get('result') == "Processed: test data":
            print("✅ PocketFlow workflow executed successfully")
            return True
        else:
            print(f"❌ PocketFlow workflow returned unexpected result: {result}")
            return False

    except Exception as e:
        print(f"❌ PocketFlow test failed: {e}")
        return False

def test_context_optimizer():
    """Test context optimization."""
    print("\n🔍 Testing context optimizer...")

    try:
        from core.context_engineering.context_optimizer import ContextOptimizer

        optimizer = ContextOptimizer()

        # Test prompt optimization
        test_content = "This is a test content for optimization."
        optimized = optimizer.optimize_context(
            content=test_content,
            content_type="text",
            max_tokens=100
        )

        if optimized.optimized_content:
            print("✅ Context optimization completed")
            print(f"   Original length: {len(test_content)}")
            print(f"   Optimized length: {len(optimized.optimized_content)}")
            return True
        else:
            print("❌ Context optimization returned empty result")
            return False

    except Exception as e:
        print(f"❌ Context optimizer test failed: {e}")
        return False

async def test_enhanced_app():
    """Test the enhanced YouTube repurposer app."""
    print("\n🔍 Testing enhanced app...")

    try:
        from examples.youtube_repurposer_app import YouTubeRepurposerApp

        # Initialize with minimal config
        config = {
            'log_level': 'INFO',
            'cache_enabled': False,
            'enable_rag': False,
            'enable_context_optimization': False,
            'enable_web_crawling': False
        }

        app = YouTubeRepurposerApp(config)
        print("✅ Enhanced app initialized")

        # Test basic workflow (will use mock LLM if no API key)
        test_transcript = "Welcome to this tutorial about machine learning. Today we'll cover the basics."

        try:
            result = await app.app.process(
                workflow_name='content_analysis',
                content=test_transcript,
                content_type='video_transcript'
            )

            if result and result.success:
                print("✅ Content analysis workflow completed")
                print(f"   Processing time: {result.processing_time:.2f}s")
            else:
                print(f"⚠️  Content analysis completed with warnings: {result.error if result else 'Unknown error'}")

        except Exception as e:
            print(f"⚠️  Content analysis failed (expected without API key): {e}")

        return True

    except Exception as e:
        print(f"❌ Enhanced app test failed: {e}")
        return False

def test_flask_integration():
    """Test Flask app integration."""
    print("\n🔍 Testing Flask integration...")

    try:
        # Import the main Flask app
        import app as flask_app

        # Check if enhanced app was initialized
        if hasattr(flask_app, 'enhanced_app') and flask_app.enhanced_app:
            print("✅ Enhanced app integrated with Flask")
        else:
            print("⚠️  Enhanced app not initialized in Flask (check API keys)")

        # Test Flask app creation
        if flask_app.app:
            print("✅ Flask app created successfully")
        else:
            print("❌ Flask app creation failed")
            return False

        return True

    except Exception as e:
        print(f"❌ Flask integration test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Enhanced YouTube Repurposer System Test")
    print("=" * 50)

    tests = [
        ("Import Test", test_imports),
        ("Environment Test", test_environment),
        ("Vector Store Test", test_vector_store),
        ("PocketFlow Test", test_pocketflow),
        ("Context Optimizer Test", test_context_optimizer),
        ("Enhanced App Test", test_enhanced_app),
        ("Flask Integration Test", test_flask_integration)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")

        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")

    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The enhanced system is ready to use.")
        print("\n🚀 Next steps:")
        print("   1. Configure API keys in .env file")
        print("   2. Run: python app.py")
        print("   3. Visit: http://localhost:5000")
    elif passed >= total * 0.7:  # 70% pass rate
        print("⚠️  Most tests passed. System should work with some limitations.")
        print("   Check failed tests and configure missing components.")
    else:
        print("❌ Many tests failed. Please check the setup and dependencies.")
        print("   Run: python setup_enhanced.py")

    print("\n📝 For detailed usage instructions, see:")
    print("   - README.md")
    print("   - TEMPLATE_USAGE.md")
    print("   - examples/create_custom_apps.py")

if __name__ == "__main__":
    asyncio.run(main())