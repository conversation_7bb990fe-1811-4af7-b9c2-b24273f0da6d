#!/usr/bin/env python3
"""
WhisperX Transcription Module

This module provides audio transcription capabilities using WhisperX,
replacing OpenAI's transcription service for better accuracy and local processing.
"""

import os
import tempfile
import subprocess
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import whisperx
    import torch
    WHISPERX_AVAILABLE = True
except ImportError:
    WHISPERX_AVAILABLE = False
    logger.warning("WhisperX not available. Install with: pip install whisperx")

@dataclass
class TranscriptionSegment:
    """Represents a segment of transcribed audio with timing information"""
    start: float
    end: float
    text: str
    confidence: Optional[float] = None
    speaker: Optional[str] = None

@dataclass
class TranscriptionResult:
    """Complete transcription result with metadata"""
    segments: List[TranscriptionSegment]
    full_text: str
    language: str
    duration: float
    word_count: int
    confidence_avg: float
    processing_time: float

class WhisperXTranscriber:
    """WhisperX-based transcription service"""

    def __init__(self,
                 model_size: str = "base",
                 device: str = "auto",
                 compute_type: str = "float16",
                 enable_diarization: bool = False,
                 hf_token: Optional[str] = None):
        """
        Initialize WhisperX transcriber

        Args:
            model_size: WhisperX model size (tiny, base, small, medium, large-v2, large-v3)
            device: Device to use (auto, cpu, cuda)
            compute_type: Computation type (float16, int8, float32)
            enable_diarization: Enable speaker diarization
            hf_token: HuggingFace token for diarization models
        """
        self.model_size = model_size
        self.device = self._get_device(device)
        self.compute_type = compute_type
        self.enable_diarization = enable_diarization
        self.hf_token = hf_token or os.getenv('HF_TOKEN')

        self.model = None
        self.align_model = None
        self.diarize_model = None

        if not WHISPERX_AVAILABLE:
            logger.error("WhisperX is not installed. Please install with: pip install whisperx")
            raise ImportError("WhisperX is required but not installed")

    def _get_device(self, device: str) -> str:
        """Determine the best device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"  # Apple Silicon
            else:
                return "cpu"
        return device

    def _load_models(self):
        """Load WhisperX models on demand"""
        if self.model is None:
            logger.info(f"Loading WhisperX model: {self.model_size} on {self.device}")
            self.model = whisperx.load_model(
                self.model_size,
                self.device,
                compute_type=self.compute_type
            )

    def _load_alignment_model(self, language: str):
        """Load alignment model for better timestamp accuracy"""
        if self.align_model is None:
            logger.info(f"Loading alignment model for language: {language}")
            self.align_model, self.align_metadata = whisperx.load_align_model(
                language_code=language,
                device=self.device
            )

    def _load_diarization_model(self):
        """Load speaker diarization model"""
        if self.enable_diarization and self.diarize_model is None:
            if not self.hf_token:
                logger.warning("HuggingFace token required for diarization. Skipping speaker identification.")
                return

            logger.info("Loading diarization model")
            self.diarize_model = whisperx.DiarizationPipeline(
                use_auth_token=self.hf_token,
                device=self.device
            )

    def extract_audio_from_video(self, video_path: str) -> str:
        """Extract audio from video file using FFmpeg"""
        try:
            # Create temporary audio file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                audio_path = temp_audio.name

            # Extract audio using FFmpeg
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM 16-bit
                '-ar', '16000',  # 16kHz sample rate
                '-ac', '1',  # Mono
                '-y',  # Overwrite output
                audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"FFmpeg error: {result.stderr}")

            logger.info(f"Audio extracted to: {audio_path}")
            return audio_path

        except Exception as e:
            logger.error(f"Failed to extract audio: {e}")
            raise

    def transcribe_file(self, file_path: str, language: Optional[str] = None) -> TranscriptionResult:
        """Transcribe audio or video file"""
        import time
        start_time = time.time()

        try:
            # Load models
            self._load_models()

            # Handle video files by extracting audio
            audio_path = file_path
            temp_audio = None

            if file_path.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.m4v')):
                logger.info("Video file detected, extracting audio...")
                audio_path = self.extract_audio_from_video(file_path)
                temp_audio = audio_path

            # Load audio
            logger.info(f"Loading audio: {audio_path}")
            audio = whisperx.load_audio(audio_path)

            # Transcribe
            logger.info("Starting transcription...")
            result = self.model.transcribe(audio, batch_size=16)
            detected_language = result.get("language", language or "en")

            # Align timestamps for better accuracy (with word-level timestamps)
            logger.info("Aligning timestamps with word-level precision...")
            self._load_alignment_model(detected_language)
            aligned_result = result  # Keep original result
            if self.align_model:
                aligned_result = whisperx.align(
                    result["segments"],
                    self.align_model,
                    self.align_metadata,
                    audio,
                    self.device,
                    return_char_alignments=False  # We want word-level, not character-level
                )
                # Store the aligned result with word-level timestamps
                self._word_level_result = aligned_result

            # Speaker diarization (optional)
            if self.enable_diarization:
                logger.info("Performing speaker diarization...")
                self._load_diarization_model()
                if self.diarize_model:
                    diarize_segments = self.diarize_model(audio)
                    aligned_result = whisperx.assign_word_speakers(diarize_segments, aligned_result)

            # Process results
            segments = []
            full_text_parts = []
            total_confidence = 0
            confidence_count = 0

            for segment in aligned_result.get("segments", []):
                text = segment.get("text", "").strip()
                if not text:
                    continue

                confidence = segment.get("avg_logprob")
                if confidence is not None:
                    # Convert log probability to confidence score (0-1)
                    confidence = max(0, min(1, (confidence + 5) / 5))  # Rough conversion
                    total_confidence += confidence
                    confidence_count += 1

                segments.append(TranscriptionSegment(
                    start=segment.get("start", 0),
                    end=segment.get("end", 0),
                    text=text,
                    confidence=confidence,
                    speaker=segment.get("speaker")
                ))

                full_text_parts.append(text)

            # Calculate metrics
            full_text = " ".join(full_text_parts)
            word_count = len(full_text.split())
            confidence_avg = total_confidence / confidence_count if confidence_count > 0 else 0
            duration = segments[-1].end if segments else 0
            processing_time = time.time() - start_time

            # Cleanup temporary audio file
            if temp_audio and os.path.exists(temp_audio):
                os.unlink(temp_audio)

            logger.info(f"Transcription completed in {processing_time:.2f}s")
            logger.info(f"Detected language: {detected_language}")
            logger.info(f"Word count: {word_count}")
            logger.info(f"Average confidence: {confidence_avg:.2f}")

            return TranscriptionResult(
                segments=segments,
                full_text=full_text,
                language=detected_language,
                duration=duration,
                word_count=word_count,
                confidence_avg=confidence_avg,
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise

    def transcribe_audio_data(self, audio_data: bytes, language: Optional[str] = None) -> TranscriptionResult:
        """Transcribe audio from raw bytes"""
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_path = temp_file.name

        try:
            return self.transcribe_file(temp_path, language)
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def export_to_vtt(self, result: TranscriptionResult, output_path: str):
        """Export transcription to WebVTT format"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("WEBVTT\n\n")

            for i, segment in enumerate(result.segments, 1):
                start_time = self._format_timestamp(segment.start)
                end_time = self._format_timestamp(segment.end)

                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")

                # Add speaker info if available
                if segment.speaker:
                    f.write(f"<v {segment.speaker}>{segment.text}</v>\n")
                else:
                    f.write(f"{segment.text}\n")

                f.write("\n")

    def export_to_srt(self, result: TranscriptionResult, output_path: str):
        """Export transcription to SRT format"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, segment in enumerate(result.segments, 1):
                start_time = self._format_timestamp_srt(segment.start)
                end_time = self._format_timestamp_srt(segment.end)

                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")

                # Add speaker info if available
                if segment.speaker:
                    f.write(f"[{segment.speaker}] {segment.text}\n")
                else:
                    f.write(f"{segment.text}\n")

                f.write("\n")

    def export_word_timestamps(self, result_with_words, output_path: str):
        """Export word-level timestamps to JSON format"""
        import json

        word_data = {
            "language": result_with_words.get("language", "unknown"),
            "segments": []
        }

        for segment in result_with_words.get("segments", []):
            segment_data = {
                "start": segment.get("start", 0),
                "end": segment.get("end", 0),
                "text": segment.get("text", ""),
                "words": []
            }

            # Extract word-level timestamps
            for word in segment.get("words", []):
                word_info = {
                    "word": word.get("word", ""),
                    "start": word.get("start", 0),
                    "end": word.get("end", 0),
                    "score": word.get("score", 0)
                }
                segment_data["words"].append(word_info)

            word_data["segments"].append(segment_data)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(word_data, f, indent=2, ensure_ascii=False)

    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp for VTT (HH:MM:SS.mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"

    def _format_timestamp_srt(self, seconds: float) -> str:
        """Format timestamp for SRT (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

    def get_model_info(self) -> Dict:
        """Get information about loaded models"""
        return {
            "model_size": self.model_size,
            "device": self.device,
            "compute_type": self.compute_type,
            "diarization_enabled": self.enable_diarization,
            "models_loaded": {
                "transcription": self.model is not None,
                "alignment": self.align_model is not None,
                "diarization": self.diarize_model is not None
            }
        }

# Factory function for easy initialization
def create_transcriber(model_size: str = "base",
                      enable_diarization: bool = False,
                      device: str = "auto") -> WhisperXTranscriber:
    """Create a WhisperX transcriber with common settings"""
    return WhisperXTranscriber(
        model_size=model_size,
        device=device,
        enable_diarization=enable_diarization
    )

# Mock transcriber for testing when WhisperX is not available
class MockTranscriber:
    """Mock transcriber for testing purposes"""

    def transcribe_file(self, file_path: str, language: Optional[str] = None) -> TranscriptionResult:
        """Mock transcription that returns sample data"""
        logger.warning("Using mock transcriber - WhisperX not available")

        # Generate mock segments
        mock_text = "This is a mock transcription. WhisperX is not available, so this is placeholder text for testing purposes."
        segments = [
            TranscriptionSegment(
                start=0.0,
                end=5.0,
                text=mock_text,
                confidence=0.95
            )
        ]

        return TranscriptionResult(
            segments=segments,
            full_text=mock_text,
            language=language or "en",
            duration=5.0,
            word_count=len(mock_text.split()),
            confidence_avg=0.95,
            processing_time=0.1
        )

    def get_model_info(self) -> Dict:
        return {"mock": True, "reason": "WhisperX not available"}

# Auto-select transcriber based on availability
def get_transcriber(**kwargs) -> WhisperXTranscriber:
    """Get the best available transcriber"""
    if WHISPERX_AVAILABLE:
        return WhisperXTranscriber(**kwargs)
    else:
        logger.warning("WhisperX not available, using mock transcriber")
        return MockTranscriber()