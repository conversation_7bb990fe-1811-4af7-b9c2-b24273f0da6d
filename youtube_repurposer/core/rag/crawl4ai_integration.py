#!/usr/bin/env python3
"""
Crawl4AI Integration for YouTube Repurposer RAG System

This module integrates Crawl4AI RAG MCP capabilities with the existing
YouTube Repurposer system, providing advanced web crawling and RAG functionality.
"""

import os
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

# Core imports
try:
    from crawl4ai import AsyncWebCrawler  # type: ignore
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None

try:
    from supabase import create_client  # type: ignore
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None  # type: ignore

try:
    from neo4j import GraphDatabase  # type: ignore
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None  # type: ignore

# Local imports
try:
    from .enhanced_rag_processor import EnhancedRAGProcessor, Document, RetrievalResult  # type: ignore
except ImportError:
    # Fallback types for when enhanced_rag_processor is not available
    EnhancedRAGProcessor = None
    Document = None
    RetrievalResult = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CrawlResult:
    """Result from a crawl operation."""
    url: str
    title: str
    content: str
    markdown_content: str
    metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None
    crawl_timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.crawl_timestamp is None:
            self.crawl_timestamp = datetime.now()

@dataclass
class RAGConfig:
    """Configuration for RAG strategies."""
    use_contextual_embeddings: bool = False
    use_hybrid_search: bool = True
    use_agentic_rag: bool = False
    use_reranking: bool = True
    use_knowledge_graph: bool = False
    embedding_model: str = "text-embedding-3-small"
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_results: int = 10
    similarity_threshold: float = 0.7

class Crawl4AIIntegration:
    """Integration class for Crawl4AI RAG MCP functionality."""

    def __init__(self, config: Optional[RAGConfig] = None):
        """Initialize the Crawl4AI integration."""
        self.config = config or RAGConfig()
        self.supabase_client: Optional[Any] = None
        self.neo4j_driver: Optional[Any] = None
        self.openai_client: Optional[Any] = None
        self.enhanced_rag: Optional[Any] = None

        # Check dependencies
        self._check_dependencies()

        # Initialize clients
        self._initialize_clients()

        # Initialize enhanced RAG processor
        if EnhancedRAGProcessor is not None:
            self.enhanced_rag = EnhancedRAGProcessor()

    def _check_dependencies(self):
        """Check if required dependencies are available."""
        missing_deps = []

        if not CRAWL4AI_AVAILABLE:
            missing_deps.append("crawl4ai")

        if not SUPABASE_AVAILABLE:
            missing_deps.append("supabase")

        if not OPENAI_AVAILABLE:
            missing_deps.append("openai")

        if self.config.use_knowledge_graph and not NEO4J_AVAILABLE:
            missing_deps.append("neo4j")

        if missing_deps:
            logger.warning(f"Missing dependencies: {', '.join(missing_deps)}")
            logger.warning("Some features may not be available. Run: pip install -r requirements.txt")

    def _initialize_clients(self):
        """Initialize external service clients."""
        # Initialize Supabase client
        if SUPABASE_AVAILABLE:
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

            if supabase_url and supabase_key:
                try:
                    self.supabase_client = create_client(supabase_url, supabase_key)
                    logger.info("Supabase client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Supabase client: {e}")
            else:
                logger.warning("Supabase credentials not found in environment")

        # Initialize Neo4j driver
        if self.config.use_knowledge_graph and NEO4J_AVAILABLE:
            neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
            neo4j_user = os.getenv("NEO4J_USER", "neo4j")
            neo4j_password = os.getenv("NEO4J_PASSWORD")

            if neo4j_password:
                try:
                    self.neo4j_driver = GraphDatabase.driver(
                        neo4j_uri, auth=(neo4j_user, neo4j_password)
                    )
                    logger.info("Neo4j driver initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Neo4j driver: {e}")
            else:
                logger.warning("Neo4j password not found in environment")

        # Initialize OpenAI client
        if OPENAI_AVAILABLE and openai is not None:
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if openai_api_key:
                try:
                    self.openai_client = openai.OpenAI(api_key=openai_api_key)
                    logger.info("OpenAI client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize OpenAI client: {e}")
            else:
                logger.warning("OpenAI API key not found in environment")

    async def crawl_single_page(self, url: str, **kwargs) -> CrawlResult:
        """Crawl a single web page."""
        if not CRAWL4AI_AVAILABLE:
            return CrawlResult(
                url=url, title="", content="", markdown_content="",
                metadata={}, success=False,
                error_message="Crawl4AI not available"
            )

        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url, **kwargs)

                if result.success:
                    crawl_result = CrawlResult(
                        url=url,
                        title=result.metadata.get("title", ""),
                        content=result.cleaned_html or "",
                        markdown_content=result.markdown or "",
                        metadata=result.metadata or {},
                        success=True
                    )

                    # Store in Supabase if available
                    await self._store_crawl_result(crawl_result)

                    return crawl_result
                else:
                    return CrawlResult(
                        url=url, title="", content="", markdown_content="",
                        metadata={}, success=False,
                        error_message=result.error_message
                    )

        except Exception as e:
            logger.error(f"Error crawling {url}: {e}")
            return CrawlResult(
                url=url, title="", content="", markdown_content="",
                metadata={}, success=False,
                error_message=str(e)
            )

    async def smart_crawl_url(self, url: str, max_pages: int = 50, **kwargs) -> List[CrawlResult]:
        """Intelligently crawl a website based on URL type."""
        results = []

        # Determine crawl strategy based on URL
        if "sitemap.xml" in url.lower():
            # Handle sitemap crawling
            results = await self._crawl_sitemap(url, max_pages, **kwargs)
        elif url.endswith(".txt") or "llms-full.txt" in url:
            # Handle text file with URLs
            results = await self._crawl_url_list(url, max_pages, **kwargs)
        else:
            # Recursive crawling
            results = await self._crawl_recursive(url, max_pages, **kwargs)

        return results

    async def _crawl_sitemap(self, sitemap_url: str, _max_pages: int, **_kwargs) -> List[CrawlResult]:
        """Crawl URLs from a sitemap."""
        # Implementation for sitemap crawling
        # This would parse the sitemap XML and crawl each URL
        logger.info(f"Crawling sitemap: {sitemap_url}")
        # Placeholder implementation
        return []

    async def _crawl_url_list(self, file_url: str, _max_pages: int, **_kwargs) -> List[CrawlResult]:
        """Crawl URLs from a text file."""
        # Implementation for URL list crawling
        logger.info(f"Crawling URL list: {file_url}")
        # Placeholder implementation
        return []

    async def _crawl_recursive(self, start_url: str, _max_pages: int, **_kwargs) -> List[CrawlResult]:
        """Recursively crawl a website."""
        # Implementation for recursive crawling
        logger.info(f"Recursive crawling: {start_url}")
        # Placeholder implementation
        return []

    async def _store_crawl_result(self, result: CrawlResult):
        """Store crawl result in Supabase."""
        if not self.supabase_client:
            return

        try:
            # Generate embedding if OpenAI client is available
            embedding = None
            if self.openai_client and result.content:
                embedding = await self._generate_embedding(result.content)

            # Prepare data for insertion
            data = {
                "url": result.url,
                "title": result.title,
                "content": result.content,
                "markdown_content": result.markdown_content,
                "metadata": result.metadata,
                "source_domain": self._extract_domain(result.url),
                "crawl_timestamp": result.crawl_timestamp.isoformat() if result.crawl_timestamp else None,
                "embedding": embedding,
                "word_count": len(result.content.split()) if result.content else 0
            }

            # Insert into Supabase
            self.supabase_client.table("crawled_pages").upsert(data).execute()
            logger.info(f"Stored crawl result for {result.url}")

        except Exception as e:
            logger.error(f"Error storing crawl result: {e}")

    async def _generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding for text using OpenAI."""
        if not self.openai_client:
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model=self.config.embedding_model
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None

    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        from urllib.parse import urlparse
        return urlparse(url).netloc

    async def perform_rag_query(self, query: str, sources: Optional[List[str]] = None, **kwargs) -> Any:
        """Perform RAG query with optional source filtering."""
        if not self.supabase_client:
            # Fallback to enhanced RAG processor
            if self.enhanced_rag is not None:
                return await self.enhanced_rag.retrieve_documents(query, **kwargs)
            else:
                raise Exception("No RAG processor available")

        try:
            # Generate query embedding
            query_embedding = await self._generate_embedding(query)
            if not query_embedding:
                raise Exception("Failed to generate query embedding")

            # Prepare source filter
            source_filter = sources[0] if sources else None

            # Perform similarity search
            response = self.supabase_client.rpc(
                "similarity_search",
                {
                    "query_embedding": query_embedding,
                    "match_threshold": self.config.similarity_threshold,
                    "match_count": self.config.max_results,
                    "source_filter": source_filter
                }
            ).execute()

            # Convert results to Document objects
            documents = []
            scores = []

            for row in response.data:
                doc = Document(
                    id=str(row["id"]),
                    content=row["content"],
                    metadata={
                        "url": row["url"],
                        "title": row["title"],
                        "source_domain": row["source_domain"],
                        "summary": row["summary"]
                    },
                    source_url=row["url"]
                )
                documents.append(doc)
                scores.append(row["similarity"])

            if RetrievalResult is not None:
                return RetrievalResult(
                    documents=documents,
                    query=query,
                    scores=scores,
                    total_results=len(documents),
                    retrieval_time=0.0  # Would need to measure actual time
                )
            else:
                return {
                    "documents": documents,
                    "query": query,
                    "scores": scores,
                    "total_results": len(documents),
                    "retrieval_time": 0.0
                }

        except Exception as e:
            logger.error(f"Error performing RAG query: {e}")
            # Fallback to enhanced RAG processor
            if self.enhanced_rag is not None:
                return await self.enhanced_rag.retrieve_documents(query, **kwargs)
            else:
                raise Exception("No RAG processor available")

    async def get_available_sources(self) -> List[Dict[str, Any]]:
        """Get list of available sources in the database."""
        if not self.supabase_client:
            return []

        try:
            response = self.supabase_client.rpc("get_available_sources").execute()
            return response.data
        except Exception as e:
            logger.error(f"Error getting available sources: {e}")
            return []

    async def search_code_examples(self, query: str, **_kwargs) -> List[Dict[str, Any]]:
        """Search for code examples (requires agentic RAG)."""
        if not self.config.use_agentic_rag:
            logger.warning("Agentic RAG not enabled")
            return []

        # Implementation for code example search
        # This would use specialized prompts and filtering
        logger.info(f"Searching code examples for: {query}")
        return []

    def close(self):
        """Close all connections."""
        if self.neo4j_driver:
            self.neo4j_driver.close()

        logger.info("Crawl4AI integration closed")

# Factory function for easy initialization
def create_crawl4ai_integration(config: Optional[Dict[str, Any]] = None) -> Crawl4AIIntegration:
    """Create and configure a Crawl4AI integration instance."""
    if config:
        rag_config = RAGConfig(**config)
    else:
        # Load from environment variables
        rag_config = RAGConfig(
            use_contextual_embeddings=os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false").lower() == "true",
            use_hybrid_search=os.getenv("USE_HYBRID_SEARCH", "true").lower() == "true",
            use_agentic_rag=os.getenv("USE_AGENTIC_RAG", "false").lower() == "true",
            use_reranking=os.getenv("USE_RERANKING", "true").lower() == "true",
            use_knowledge_graph=os.getenv("USE_KNOWLEDGE_GRAPH", "false").lower() == "true",
            embedding_model=os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        )

    return Crawl4AIIntegration(rag_config)

# Example usage
if __name__ == "__main__":
    async def main():
        # Create integration instance
        integration = create_crawl4ai_integration()

        # Example: Crawl a single page
        result = await integration.crawl_single_page("https://example.com")
        print(f"Crawl result: {result.success}")

        # Example: Perform RAG query
        rag_result = await integration.perform_rag_query("What is machine learning?")
        print(f"Found {len(rag_result.documents)} relevant documents")

        # Close connections
        integration.close()

    # Run example
    asyncio.run(main())