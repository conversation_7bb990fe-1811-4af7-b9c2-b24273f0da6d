# Universal Content Application Template
# Integrates Pocket<PERSON>low, Enhanced RAG, and Context Engineering
from __future__ import annotations

import os
from typing import Dict, List, Any, Optional, Callable, Type, TYPE_CHECKING
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# Import our core modules
if TYPE_CHECKING:
    from .pocketflow import PocketFlow, FunctionNode, ConditionalNode
    from .rag.enhanced_rag_processor import EnhancedRAGProcessor
    from .context_engineering.context_optimizer import ContextOptimizer, ContentType
    from .agents.content_analyzer import ContentAnalyzer
else:
    # Runtime imports with fallbacks
    try:
        from .pocketflow import PocketFlow, FunctionNode, ConditionalNode
    except ImportError:
        PocketFlow = FunctionNode = ConditionalNode = None  # type: ignore

    try:
        from .rag.enhanced_rag_processor import EnhancedRAGProcessor
    except ImportError:
        EnhancedRAGProcessor = None  # type: ignore

    try:
        from .context_engineering.context_optimizer import ContextOptimizer, ContentType
    except ImportError:
        ContextOptimizer = ContentType = None  # type: ignore

    try:
        from .agents.content_analyzer import ContentAnalyzer
    except ImportError:
        ContentAnalyzer = None  # type: ignore

# Import existing components
DeepSeekClass: Optional[Type[Any]] = None
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from deepseek import DeepSeek
    DeepSeekClass = DeepSeek
except ImportError:
    pass

@dataclass
class AppConfig:
    """Configuration for content applications"""
    app_name: str
    app_type: str  # 'content_repurposer', 'social_optimizer', 'script_generator', etc.
    llm_provider: str = 'deepseek'
    max_context_tokens: int = 8000
    enable_rag: bool = True
    enable_web_crawling: bool = True
    enable_context_optimization: bool = True
    vector_store_path: Optional[str] = None
    cache_enabled: bool = True
    log_level: str = 'INFO'
    custom_workflows: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.vector_store_path is None:
            self.vector_store_path = f"{self.app_name}_vector_store.db"
        if self.custom_workflows is None:
            self.custom_workflows = {}

@dataclass
class ProcessingResult:
    """Result of content processing"""
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    processing_time: float
    workflow_used: str
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []

class ContentApplicationTemplate:
    """Universal template for creating content-focused applications"""

    def __init__(self, config: AppConfig):
        self.config = config
        self.logger = self._setup_logging()

        # Initialize core components
        self.llm_client = self._initialize_llm()
        self.rag_processor = None
        self.context_optimizer = None
        self.content_analyzer = None

        # Workflow registry
        self.workflows: Dict[str, Dict[str, Any]] = {}
        self.middleware: List[Callable] = []

        # Initialize components based on config
        self._initialize_components()

        # Register default workflows
        self._register_default_workflows()

        # Register custom workflows
        self._register_custom_workflows()

    def _setup_logging(self) -> logging.Logger:
        """Setup application logging"""
        logger = logging.getLogger(self.config.app_name)
        logger.setLevel(getattr(logging, self.config.log_level))

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _initialize_llm(self):
        """Initialize LLM client based on configuration"""
        if self.config.llm_provider == 'deepseek' and DeepSeekClass is not None:
            return DeepSeekClass()
        else:
            # Fallback or mock implementation
            self.logger.warning(f"LLM provider {self.config.llm_provider} not available, using mock")
            return self._create_mock_llm()

    def _create_mock_llm(self):
        """Create a mock LLM for testing"""
        class MockLLM:
            class Chat:
                class Completions:
                    async def create(self, **_kwargs: Any):
                        # _kwargs contains model parameters but we ignore them for mock
                        class MockResponse:
                            class Choice:
                                class MockMessage:
                                    content = "Mock response for testing purposes."
                                message = MockMessage()
                            choices = [Choice()]
                        return MockResponse()
                completions = Completions()
            chat = Chat()
        return MockLLM()

    def _initialize_components(self):
        """Initialize core components based on configuration"""
        try:
            # Initialize RAG processor
            if self.config.enable_rag and EnhancedRAGProcessor is not None:
                self.rag_processor = EnhancedRAGProcessor(
                    self.llm_client,
                    self.config.vector_store_path
                )
                self.logger.info("RAG processor initialized")
            else:
                if self.config.enable_rag:
                    self.logger.warning("RAG processor not available")

            # Initialize context optimizer
            if self.config.enable_context_optimization and ContextOptimizer is not None:
                self.context_optimizer = ContextOptimizer(
                    max_context_tokens=self.config.max_context_tokens
                )
                self.logger.info("Context optimizer initialized")
            else:
                if self.config.enable_context_optimization:
                    self.logger.warning("Context optimizer not available")

            # Initialize content analyzer
            if ContentAnalyzer is not None:
                self.content_analyzer = ContentAnalyzer(
                    self.llm_client,
                    self.rag_processor
                )
                self.logger.info("Content analyzer initialized")
            else:
                self.logger.warning("Content analyzer not available")

        except Exception as e:
            self.logger.error(f"Error initializing components: {e}")
            raise

    def _register_default_workflows(self):
        """Register default workflows for common tasks"""

        # Content Analysis Workflow
        self.register_workflow(
            'content_analysis',
            self._create_content_analysis_workflow(),
            description="Analyze content for structure, engagement, and optimization opportunities"
        )

        # Content Repurposing Workflow
        self.register_workflow(
            'content_repurposing',
            self._create_content_repurposing_workflow(),
            description="Transform content into multiple formats and platforms"
        )

        # SEO Optimization Workflow
        self.register_workflow(
            'seo_optimization',
            self._create_seo_optimization_workflow(),
            description="Optimize content for search engines and discoverability"
        )

        # Social Media Optimization Workflow
        self.register_workflow(
            'social_optimization',
            self._create_social_optimization_workflow(),
            description="Optimize content for social media platforms"
        )

    def _create_content_analysis_workflow(self):
        """Create content analysis workflow using PocketFlow"""
        if PocketFlow is None or FunctionNode is None or ConditionalNode is None:
            self.logger.warning("PocketFlow not available, using simple workflow")
            return self._create_simple_workflow()

        flow = PocketFlow()

        # Step 1: Context optimization
        context_node = FunctionNode('context_optimization', self._optimize_context_step)

        # Step 2: Content analysis
        analysis_node = FunctionNode('content_analysis', self._analyze_content_step)

        # Step 3: RAG enhancement (conditional)
        rag_condition = ConditionalNode(
            'rag_check',
            lambda state: self.config.enable_rag and self.rag_processor is not None
        )
        rag_condition.if_true(['rag_enhancement']).if_false(['compile_results'])

        rag_node = FunctionNode('rag_enhancement', self._enhance_with_rag_step)

        # Step 4: Results compilation
        compilation_node = FunctionNode('compile_results', self._compile_analysis_results)

        # Build workflow
        flow.add_node(context_node)
        flow.add_node(analysis_node)
        flow.add_node(rag_condition)
        flow.add_node(rag_node)
        flow.add_node(compilation_node)

        flow.add_edge('context_optimization', 'content_analysis')
        flow.add_edge('content_analysis', 'rag_check')
        flow.add_edge('rag_enhancement', 'compile_results')

        flow.set_start('context_optimization')

        return flow

    def _create_simple_workflow(self):
        """Create a simple workflow fallback when PocketFlow is not available"""
        # Return a simple object with a run method
        class SimpleWorkflow:
            def __init__(self, parent: Any):
                self.parent = parent

            async def run(self, state: Dict[str, Any]) -> Dict[str, Any]:
                # Simple sequential execution
                state = await self.parent._optimize_context_step(state)
                state = await self.parent._analyze_content_step(state)
                if self.parent.config.enable_rag and self.parent.rag_processor is not None:
                    state = await self.parent._enhance_with_rag_step(state)
                state = await self.parent._compile_analysis_results(state)
                return state

        return SimpleWorkflow(self)

    def _create_content_repurposing_workflow(self):
        """Create content repurposing workflow"""
        if PocketFlow is None or FunctionNode is None:
            return self._create_simple_workflow()

        flow = PocketFlow()

        # Step 1: Content analysis
        analysis_node = FunctionNode('analyze_content', self._analyze_for_repurposing)

        # Step 2: Format identification
        format_node = FunctionNode('identify_formats', self._identify_target_formats)

        # Step 3: Content transformation
        transform_node = FunctionNode('transform_content', self._transform_content)

        # Step 4: Platform optimization
        optimize_node = FunctionNode('optimize_platforms', self._optimize_for_platforms)

        # Build workflow
        flow.add_node(analysis_node)
        flow.add_node(format_node)
        flow.add_node(transform_node)
        flow.add_node(optimize_node)

        flow.add_edge('analyze_content', 'identify_formats')
        flow.add_edge('identify_formats', 'transform_content')
        flow.add_edge('transform_content', 'optimize_platforms')

        flow.set_start('analyze_content')

        return flow

    def _create_seo_optimization_workflow(self):
        """Create SEO optimization workflow"""
        if PocketFlow is None or FunctionNode is None:
            return self._create_simple_workflow()

        flow = PocketFlow()

        # Step 1: Keyword research
        keyword_node = FunctionNode('keyword_research', self._research_keywords)

        # Step 2: Content optimization
        seo_node = FunctionNode('seo_optimization', self._optimize_for_seo)

        # Step 3: Meta data generation
        meta_node = FunctionNode('generate_metadata', self._generate_seo_metadata)

        # Build workflow
        flow.add_node(keyword_node)
        flow.add_node(seo_node)
        flow.add_node(meta_node)

        flow.add_edge('keyword_research', 'seo_optimization')
        flow.add_edge('seo_optimization', 'generate_metadata')

        flow.set_start('keyword_research')

        return flow

    def _create_social_optimization_workflow(self):
        """Create social media optimization workflow"""
        if PocketFlow is None or FunctionNode is None:
            return self._create_simple_workflow()

        flow = PocketFlow()

        # Step 1: Platform analysis
        platform_node = FunctionNode('analyze_platforms', self._analyze_social_platforms)

        # Step 2: Content adaptation
        adapt_node = FunctionNode('adapt_content', self._adapt_for_social)

        # Step 3: Engagement optimization
        engagement_node = FunctionNode('optimize_engagement', self._optimize_social_engagement)

        # Build workflow
        flow.add_node(platform_node)
        flow.add_node(adapt_node)
        flow.add_node(engagement_node)

        flow.add_edge('analyze_platforms', 'adapt_content')
        flow.add_edge('adapt_content', 'optimize_engagement')

        flow.set_start('analyze_platforms')

        return flow

    # Workflow step implementations
    async def _optimize_context_step(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize context for processing"""
        if not self.context_optimizer:
            return state

        try:
            content = state.get('content', '')
            task_type = state.get('task_type', 'content_analysis')

            if ContentType is not None:
                content_type = ContentType(state.get('content_type', 'video_transcript'))
            else:
                content_type = state.get('content_type', 'video_transcript')

            optimized = self.context_optimizer.optimize_context(
                content=content,
                task_type=task_type,
                content_type=content_type
            )

            state['optimized_prompt'] = optimized.final_prompt
            state['optimization_notes'] = optimized.optimization_notes
            state['confidence_score'] = optimized.confidence_score

        except Exception as e:
            self.logger.error(f"Context optimization failed: {e}")
            state['optimization_error'] = str(e)

        return state

    async def _analyze_content_step(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content using the content analyzer"""
        try:
            if not self.content_analyzer:
                state['analysis_error'] = "Content analyzer not available"
                return state

            content = state.get('content', '')
            metadata = state.get('metadata', {})

            analysis = await self.content_analyzer.analyze_content(content, metadata)

            state['content_analysis'] = analysis
            state['analysis_summary'] = self.content_analyzer.get_analysis_summary(analysis)

        except Exception as e:
            self.logger.error(f"Content analysis failed: {e}")
            state['analysis_error'] = str(e)

        return state

    async def _enhance_with_rag_step(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance analysis with RAG-retrieved context"""
        if not self.rag_processor:
            return state

        try:
            analysis = state.get('content_analysis')
            if analysis and hasattr(analysis, 'main_topics'):
                # Retrieve context for main topics
                rag_context = {}
                for topic in analysis.main_topics[:3]:  # Limit to top 3 topics
                    context = await self.rag_processor.retrieve_context(topic)
                    rag_context[topic] = context

                state['rag_context'] = rag_context

        except Exception as e:
            self.logger.error(f"RAG enhancement failed: {e}")
            state['rag_error'] = str(e)

        return state

    async def _compile_analysis_results(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Compile final analysis results"""
        try:
            results = {
                'analysis': state.get('content_analysis'),
                'summary': state.get('analysis_summary'),
                'optimization_notes': state.get('optimization_notes', []),
                'confidence_score': state.get('confidence_score', 0.5),
                'rag_enhanced': 'rag_context' in state,
                'processing_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'workflow': 'content_analysis',
                    'app_name': self.config.app_name
                }
            }

            if 'rag_context' in state:
                results['rag_context'] = state['rag_context']

            state['final_results'] = results

        except Exception as e:
            self.logger.error(f"Results compilation failed: {e}")
            state['compilation_error'] = str(e)

        return state

    # Additional workflow steps (simplified implementations)
    async def _analyze_for_repurposing(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content for repurposing opportunities"""
        # Implementation would analyze content structure and identify repurposing opportunities
        state['repurposing_analysis'] = {'formats': ['short_video', 'blog_post', 'social_posts']}
        return state

    async def _identify_target_formats(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Identify target formats for content"""
        state['target_formats'] = ['tiktok', 'youtube_shorts', 'instagram_reels', 'twitter_thread']
        return state

    async def _transform_content(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Transform content into different formats"""
        state['transformed_content'] = {'tiktok': 'TikTok version', 'twitter': 'Twitter thread'}
        return state

    async def _optimize_for_platforms(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize content for specific platforms"""
        state['platform_optimized'] = True
        return state

    async def _research_keywords(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Research relevant keywords"""
        state['keywords'] = ['content creation', 'video editing', 'social media']
        return state

    async def _optimize_for_seo(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize content for SEO"""
        state['seo_optimized'] = True
        return state

    async def _generate_seo_metadata(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate SEO metadata"""
        state['seo_metadata'] = {'title': 'SEO Title', 'description': 'SEO Description'}
        return state

    async def _analyze_social_platforms(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze social media platforms"""
        state['platform_analysis'] = {'best_platforms': ['tiktok', 'instagram']}
        return state

    async def _adapt_for_social(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt content for social media"""
        state['social_adapted'] = True
        return state

    async def _optimize_social_engagement(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize for social media engagement"""
        state['engagement_optimized'] = True
        return state

    # Public API methods
    def register_workflow(self, name: str, workflow: Any, description: str = ""):
        """Register a new workflow"""
        self.workflows[name] = {
            'workflow': workflow,
            'description': description,
            'registered_at': datetime.now()
        }
        self.logger.info(f"Registered workflow: {name}")

    def add_middleware(self, middleware_func: Callable):
        """Add middleware function to processing pipeline"""
        self.middleware.append(middleware_func)

    async def process(self,
                     workflow_name: str,
                     content: str,
                     metadata: Optional[Dict[str, Any]] = None,
                     **kwargs) -> ProcessingResult:
        """Process content using specified workflow"""
        start_time = datetime.now()

        if workflow_name not in self.workflows:
            return ProcessingResult(
                success=False,
                data={},
                metadata={},
                processing_time=0,
                workflow_used=workflow_name,
                errors=[f"Workflow '{workflow_name}' not found"]
            )

        try:
            # Prepare initial state
            initial_state = {
                'content': content,
                'metadata': metadata or {},
                'workflow_name': workflow_name,
                **kwargs
            }

            # Apply middleware
            for middleware in self.middleware:
                initial_state = await middleware(initial_state)

            # Run workflow
            workflow = self.workflows[workflow_name]['workflow']
            result_state = await workflow.run(initial_state)

            processing_time = (datetime.now() - start_time).total_seconds()

            return ProcessingResult(
                success=True,
                data=result_state.get('final_results', result_state),
                metadata={
                    'workflow_name': workflow_name,
                    'processing_time': processing_time,
                    'app_config': asdict(self.config)
                },
                processing_time=processing_time,
                workflow_used=workflow_name
            )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"Workflow processing failed: {e}")

            return ProcessingResult(
                success=False,
                data={},
                metadata={'error_details': str(e)},
                processing_time=processing_time,
                workflow_used=workflow_name,
                errors=[str(e)]
            )

    def _register_custom_workflows(self):
        """Register custom workflows from configuration"""
        for name, workflow_config in self.config.custom_workflows.items():
            try:
                # This would implement custom workflow creation from config
                # For now, just log that custom workflows are configured
                self.logger.info(f"Custom workflow configured: {name} with config: {workflow_config}")
            except Exception as e:
                self.logger.error(f"Failed to register custom workflow {name}: {e}")

    def get_available_workflows(self) -> Dict[str, str]:
        """Get list of available workflows with descriptions"""
        return {
            name: info['description']
            for name, info in self.workflows.items()
        }

    def get_app_stats(self) -> Dict[str, Any]:
        """Get application statistics"""
        return {
            'app_name': self.config.app_name,
            'app_type': self.config.app_type,
            'workflows_registered': len(self.workflows),
            'middleware_count': len(self.middleware),
            'components_enabled': {
                'rag': self.config.enable_rag,
                'context_optimization': self.config.enable_context_optimization,
                'web_crawling': self.config.enable_web_crawling
            }
        }

    async def cleanup(self):
        """Cleanup application resources"""
        if self.rag_processor:
            await self.rag_processor.cleanup()

        if self.context_optimizer:
            self.context_optimizer.clear_cache()

        self.logger.info(f"Application {self.config.app_name} cleaned up")

# Factory function for creating specialized applications
def create_content_app(app_type: str, app_name: str, **config_overrides) -> ContentApplicationTemplate:
    """Factory function to create specialized content applications"""

    # Predefined configurations for different app types
    app_configs = {
        'youtube_repurposer': {
            'enable_rag': True,
            'enable_web_crawling': True,
            'enable_context_optimization': True,
            'max_context_tokens': 8000
        },
        'social_optimizer': {
            'enable_rag': True,
            'enable_web_crawling': False,
            'enable_context_optimization': True,
            'max_context_tokens': 4000
        },
        'script_generator': {
            'enable_rag': True,
            'enable_web_crawling': True,
            'enable_context_optimization': True,
            'max_context_tokens': 12000
        },
        'content_analyzer': {
            'enable_rag': False,
            'enable_web_crawling': False,
            'enable_context_optimization': True,
            'max_context_tokens': 6000
        }
    }

    # Get base config for app type
    base_config = app_configs.get(app_type, {})

    # Merge with overrides
    final_config = {**base_config, **config_overrides}

    # Create app config
    config = AppConfig(
        app_name=app_name,
        app_type=app_type,
        **final_config
    )

    return ContentApplicationTemplate(config)

# Example usage
if __name__ == "__main__":
    async def example_usage():
        # Create a YouTube repurposer app
        app = create_content_app(
            app_type='youtube_repurposer',
            app_name='my_repurposer',
            log_level='DEBUG'
        )

        # Process some content
        sample_content = """
        Today I want to share the most powerful productivity technique I've discovered.
        It's called the Pomodoro Technique and it completely changed how I work.
        The basic idea is simple: work for 25 minutes, then take a 5-minute break.
        """

        result = await app.process(
            workflow_name='content_analysis',
            content=sample_content,
            content_type='video_transcript'
        )

        print(f"Processing successful: {result.success}")
        print(f"Processing time: {result.processing_time:.2f}s")
        print(f"Available workflows: {list(app.get_available_workflows().keys())}")

        await app.cleanup()

    # Run example
    # asyncio.run(example_usage())
    pass