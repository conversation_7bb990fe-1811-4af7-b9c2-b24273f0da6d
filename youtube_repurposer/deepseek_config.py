#!/usr/bin/env python3
"""
DeepSeek R1 Configuration for YouTube Repurposer
Handles long-form content with 128k token context window optimization
"""

import os
import json
import tiktoken
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

import asyncio
import openai
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(".env")

@dataclass
class DeepSeekConfig:
    """Configuration for DeepSeek R1 integration"""
    api_key: str
    base_url: str = "https://api.deepseek.com"
    model: str = "deepseek-reasoner"
    max_tokens: int = 128000  # 128k context window
    chunk_size: int = 100000  # Leave room for prompts and responses
    overlap_tokens: int = 2000  # Overlap between chunks for continuity
    temperature: float = 0.7
    max_retries: int = 3
    timeout: int = 120  # 2 minutes timeout for long content

class TokenManager:
    """Manages token counting and chunking for DeepSeek R1"""

    def __init__(self, model_name: str = "gpt-4"):
        # Use GPT-4 tokenizer as approximation for DeepSeek
        try:
            self.encoding = tiktoken.encoding_for_model(model_name)
        except KeyError:
            self.encoding = tiktoken.get_encoding("cl100k_base")

    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))

    def chunk_text(self, text: str, chunk_size: int, overlap: int = 0) -> List[str]:
        """Split text into chunks with optional overlap"""
        tokens = self.encoding.encode(text)
        chunks = []

        start = 0
        while start < len(tokens):
            end = min(start + chunk_size, len(tokens))
            chunk_tokens = tokens[start:end]
            chunk_text = self.encoding.decode(chunk_tokens)
            chunks.append(chunk_text)

            if end >= len(tokens):
                break

            start = end - overlap

        return chunks

    def smart_chunk_transcript(self, transcript: str, max_chunk_tokens: int) -> List[Dict[str, Any]]:
        """Intelligently chunk transcript preserving sentence boundaries"""
        # Split by sentences first
        sentences = self._split_sentences(transcript)
        chunks = []
        current_chunk = ""
        current_tokens = 0

        for sentence in sentences:
            sentence_tokens = self.count_tokens(sentence)

            # If adding this sentence would exceed limit, start new chunk
            if current_tokens + sentence_tokens > max_chunk_tokens and current_chunk:
                chunks.append({
                    'text': current_chunk.strip(),
                    'token_count': current_tokens,
                    'chunk_index': len(chunks)
                })
                current_chunk = sentence
                current_tokens = sentence_tokens
            else:
                current_chunk += " " + sentence
                current_tokens += sentence_tokens

        # Add final chunk
        if current_chunk:
            chunks.append({
                'text': current_chunk.strip(),
                'token_count': current_tokens,
                'chunk_index': len(chunks)
            })

        return chunks

    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        import re
        # Simple sentence splitting - can be enhanced with NLTK if needed
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]

class DeepSeekClient:
    """Enhanced client for DeepSeek R1 with long content support"""

    def __init__(self, config: DeepSeekConfig):
        self.config = config
        self.token_manager = TokenManager()

        # Initialize OpenAI client with DeepSeek endpoint
        self.client = openai.AsyncOpenAI(
            api_key=config.api_key,
            base_url=config.base_url
        )

    async def analyze_long_transcript(self, transcript: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Analyze long transcript using chunking strategy"""

        # Check if transcript fits in single request
        total_tokens = self.token_manager.count_tokens(transcript)

        if total_tokens <= self.config.chunk_size:
            # Single request analysis
            return await self._single_request_analysis(transcript, analysis_type)
        else:
            # Multi-chunk analysis with synthesis
            return await self._multi_chunk_analysis(transcript, analysis_type)

    async def _single_request_analysis(self, transcript: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze transcript in single request"""
        prompt = self._get_analysis_prompt(transcript, analysis_type)

        try:
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": "You are an expert content analyst specializing in YouTube video analysis and short-form content creation."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )

            result = response.choices[0].message.content
            return self._parse_analysis_result(result)

        except Exception as e:
            print(f"DeepSeek analysis error: {e}")
            return {"error": str(e), "analysis": None}

    async def _multi_chunk_analysis(self, transcript: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze transcript using multiple chunks and synthesize results"""

        # Chunk the transcript
        chunks = self.token_manager.smart_chunk_transcript(
            transcript,
            self.config.chunk_size - 2000  # Leave room for prompts
        )

        print(f"Analyzing transcript in {len(chunks)} chunks...")

        # Analyze each chunk
        chunk_analyses = []
        for i, chunk in enumerate(chunks):
            print(f"Analyzing chunk {i+1}/{len(chunks)}...")

            chunk_prompt = self._get_chunk_analysis_prompt(chunk['text'], i, len(chunks), analysis_type)

            try:
                response = await self.client.chat.completions.create(
                    model=self.config.model,
                    messages=[
                        {"role": "system", "content": "You are an expert content analyst. Analyze this chunk of a longer transcript."},
                        {"role": "user", "content": chunk_prompt}
                    ],
                    max_tokens=3000,
                    temperature=self.config.temperature,
                    timeout=self.config.timeout
                )

                chunk_result = response.choices[0].message.content
                chunk_analyses.append({
                    'chunk_index': i,
                    'analysis': self._parse_analysis_result(chunk_result),
                    'token_count': chunk['token_count']
                })

                # Small delay to avoid rate limiting
                await asyncio.sleep(0.5)

            except Exception as e:
                print(f"Error analyzing chunk {i}: {e}")
                chunk_analyses.append({
                    'chunk_index': i,
                    'analysis': {"error": str(e)},
                    'token_count': chunk['token_count']
                })

        # Synthesize results from all chunks
        return await self._synthesize_chunk_analyses(chunk_analyses, analysis_type)

    async def _synthesize_chunk_analyses(self, chunk_analyses: List[Dict], _analysis_type: str) -> Dict[str, Any]:
        """Synthesize analyses from multiple chunks into final result"""

        # Prepare synthesis prompt
        synthesis_data = {
            'total_chunks': len(chunk_analyses),
            'successful_analyses': len([c for c in chunk_analyses if 'error' not in c['analysis']]),
            'chunk_summaries': []
        }

        for chunk_analysis in chunk_analyses:
            if 'error' not in chunk_analysis['analysis']:
                synthesis_data['chunk_summaries'].append({
                    'chunk_index': chunk_analysis['chunk_index'],
                    'key_points': chunk_analysis['analysis'].get('key_points', []),
                    'segments': chunk_analysis['analysis'].get('segments', []),
                    'topics': chunk_analysis['analysis'].get('topics', [])
                })

        synthesis_prompt = f"""
You are synthesizing analysis results from {synthesis_data['total_chunks']} chunks of a long YouTube transcript.

Chunk Analysis Summary:
{json.dumps(synthesis_data, indent=2)}

Your task is to create a comprehensive final analysis that:
1. Identifies the best 3-5 segments for short-form content across all chunks
2. Provides overall content themes and topics
3. Suggests optimal hooks and conclusions
4. Ranks segments by engagement potential

Return results in this JSON format:
{{
    "overall_analysis": {{
        "content_type": "string",
        "main_topics": ["topic1", "topic2"],
        "target_audience": "string",
        "total_duration_estimate": "string"
    }},
    "top_segments": [
        {{
            "title": "Compelling title",
            "hook": "Attention-grabbing opening",
            "context": "Main content",
            "conclusion": "Strong ending",
            "estimated_start": "MM:SS",
            "estimated_end": "MM:SS",
            "engagement_score": 0.85,
            "topics": ["topic1"],
            "source_chunk": 0
        }}
    ],
    "repurposing_strategy": {{
        "platform_recommendations": {{}},
        "content_calendar_suggestions": [],
        "engagement_optimization": []
    }}
}}
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": "You are an expert at synthesizing content analysis from multiple sources."},
                    {"role": "user", "content": synthesis_prompt}
                ],
                max_tokens=4000,
                temperature=0.3,  # Lower temperature for synthesis
                timeout=self.config.timeout
            )

            result = response.choices[0].message.content
            return self._parse_analysis_result(result)

        except Exception as e:
            print(f"Synthesis error: {e}")
            # Fallback: combine chunk results manually
            return self._manual_synthesis(chunk_analyses)

    def _get_analysis_prompt(self, transcript: str, _analysis_type: str) -> str:
        """Get analysis prompt for single request"""
        return f"""
Analyze this YouTube transcript for short-form content creation opportunities.

CRITICAL REQUIREMENTS:
- Each segment MUST be 30-180 seconds maximum (3 minutes max)
- Prioritize segments 60-120 seconds for optimal engagement
- If natural segments are longer, split them into multiple shorter segments

Transcript:
{transcript}

Provide analysis in JSON format:
{{
    "content_analysis": {{
        "content_type": "educational/entertainment/tutorial/etc",
        "main_topics": ["topic1", "topic2"],
        "target_audience": "description",
        "overall_sentiment": "positive/neutral/negative",
        "key_themes": ["theme1", "theme2"]
    }},
    "segments": [
        {{
            "title": "Compelling title",
            "hook": "Attention-grabbing opening (5-15 seconds)",
            "context": "Main content (15-90 seconds)",
            "conclusion": "Strong ending (5-15 seconds)",
            "start_time": "MM:SS",
            "end_time": "MM:SS",
            "engagement_score": 0.85,
            "topics": ["topic1"],
            "complexity_level": "beginner/intermediate/advanced",
            "viral_potential": "high/medium/low"
        }}
    ],
    "optimization_suggestions": [
        "suggestion1",
        "suggestion2"
    ]
}}

SEGMENT DURATION RULES:
- Total segment duration: 30-180 seconds (0:30 - 3:00)
- Optimal range: 60-120 seconds (1:00 - 2:00)
- Hook: 5-15 seconds
- Context: 15-90 seconds (reduced from 150)
- Conclusion: 5-15 seconds

Focus on identifying 4-6 segments with highest engagement potential and STRICT duration compliance.
"""

    def _get_chunk_analysis_prompt(self, chunk_text: str, chunk_index: int, total_chunks: int, _analysis_type: str) -> str:
        """Get analysis prompt for individual chunk"""
        return f"""
This is chunk {chunk_index + 1} of {total_chunks} from a longer YouTube transcript.

Chunk content:
{chunk_text}

Analyze this chunk for:
1. Key topics and themes
2. Potential short-form content segments
3. Notable quotes or moments
4. Engagement opportunities

Return JSON format:
{{
    "chunk_summary": {{
        "main_topics": ["topic1", "topic2"],
        "key_points": ["point1", "point2"],
        "sentiment": "positive/neutral/negative"
    }},
    "segments": [
        {{
            "title": "title",
            "hook": "hook",
            "context": "context",
            "conclusion": "conclusion",
            "engagement_score": 0.8,
            "topics": ["topic1"]
        }}
    ],
    "notable_quotes": ["quote1", "quote2"]
}}
"""

    def _parse_analysis_result(self, result: str) -> Dict[str, Any]:
        """Parse JSON result from DeepSeek response"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {"raw_response": result, "parsed": False}
        except json.JSONDecodeError:
            return {"raw_response": result, "parsed": False}

    def _manual_synthesis(self, chunk_analyses: List[Dict]) -> Dict[str, Any]:
        """Manual fallback synthesis when AI synthesis fails"""
        all_segments = []
        all_topics = set()

        for chunk_analysis in chunk_analyses:
            if 'error' not in chunk_analysis['analysis']:
                segments = chunk_analysis['analysis'].get('segments', [])
                all_segments.extend(segments)

                topics = chunk_analysis['analysis'].get('topics', [])
                all_topics.update(topics)

        # Sort segments by engagement score
        all_segments.sort(key=lambda x: x.get('engagement_score', 0), reverse=True)

        return {
            "overall_analysis": {
                "content_type": "multi_part_content",
                "main_topics": list(all_topics)[:10],
                "synthesis_method": "manual_fallback"
            },
            "top_segments": all_segments[:5],
            "total_segments_found": len(all_segments)
        }

def create_deepseek_client() -> Optional[DeepSeekClient]:
    """Create DeepSeek client from environment variables"""
    api_key = os.getenv('DEEPSEEK_API_KEY')

    if not api_key:
        print("Warning: DEEPSEEK_API_KEY not found in environment variables")
        return None

    config = DeepSeekConfig(
        api_key=api_key,
        base_url=os.getenv('DEEPSEEK_BASE_URL', "https://api.deepseek.com"),
        model=os.getenv('DEEPSEEK_MODEL', "deepseek-reasoner"),
        max_tokens=int(os.getenv('DEEPSEEK_MAX_TOKENS', '128000')),
        chunk_size=int(os.getenv('DEEPSEEK_CHUNK_SIZE', '100000')),
        temperature=float(os.getenv('DEEPSEEK_TEMPERATURE', '0.7'))
    )

    return DeepSeekClient(config)

# Example usage and testing
if __name__ == "__main__":
    async def test_deepseek_integration():
        client = create_deepseek_client()
        if not client:
            print("Cannot test - no API key configured")
            return

        # Test with sample transcript
        sample_transcript = """
        Welcome to today's video about artificial intelligence and its impact on content creation.
        In this comprehensive guide, we'll explore how AI is revolutionizing the way we create,
        edit, and distribute content across multiple platforms. First, let's talk about the
        current state of AI in content creation...
        """

        result = await client.analyze_long_transcript(sample_transcript)
        print("Analysis result:")
        print(json.dumps(result, indent=2))

    # Run test if script is executed directly
    asyncio.run(test_deepseek_integration())