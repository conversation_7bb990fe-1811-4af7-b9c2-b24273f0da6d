import os
import re
import openai
from dotenv import load_dotenv

load_dotenv(".env")

openai.api_key = os.getenv('OPENAI_API_KEY')

def analyze_transcript(transcript):
    """Use AI to identify structured segments for Shorts"""
    if not transcript:
        return None

    try:
        response = openai.ChatCompletion.create(
            model="gpt-4-turbo",
            messages=[
                {"role": "system", "content": "You are a YouTube content analyst. Identify engaging segments (30-180 seconds) from this transcript that follow this structure:\n1. HOOK: Attention-grabbing opening (5-15 seconds)\n2. CONTEXT: Supporting information (20-150 seconds)\n3. CONCLUSION: Main point/summary that encourages sharing (5-15 seconds)\n\nFor each segment, provide:\n- Start: [start time in seconds]\n- End: [end time in seconds]\n- Hook: [text of hook]\n- Context: [text of context]\n- Conclusion: [text of conclusion]\n- Title: [short title]\n\nFormat:\nSegment 1:\n- Start: [time]\n- End: [time]\n- Hook: [text]\n- Context: [text]\n- Conclusion: [text]\n- Title: [text]\n\n[Repeat for other segments]"},
                {"role": "user", "content": transcript[:15000]}
            ],
            temperature=0.7,
            max_tokens=2000
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"AI analysis error: {e}")
        return None

def parse_ai_response(ai_response):
    """Convert AI response into structured data"""
    if not ai_response:
        return []

    segments = []
    pattern = r"Segment \d+:\n- Start: (\d+\.?\d*)\n- End: (\d+\.?\d*)\n- Hook: (.+?)\n- Context: (.+?)\n- Conclusion: (.+?)\n- Title: (.+?)(?:\n\n|$)"
    matches = re.findall(pattern, ai_response, re.DOTALL)

    for match in matches:
        segments.append({
            "start": float(match[0]),
            "end": float(match[1]),
            "hook": match[2].strip(),
            "context": match[3].strip(),
            "conclusion": match[4].strip(),
            "title": match[5].strip()
        })

    return segments